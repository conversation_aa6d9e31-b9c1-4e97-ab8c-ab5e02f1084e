export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export interface Database {
  public: {
    Tables: {
      clients: {
        Row: {
          id: string
          created_at: string
          updated_at: string
          first_name: string
          last_name: string
          email: string | null
          phone: string | null
          dog_name: string | null
          other_dogs: string[] | null
          address: string | null
          active: boolean
          membership: boolean
        }
        Insert: {
          id?: string
          created_at?: string
          updated_at?: string
          first_name: string
          last_name: string
          email?: string | null
          phone?: string | null
          dog_name?: string | null
          other_dogs?: string[] | null
          address?: string | null
          active?: boolean
          membership?: boolean
        }
        Update: {
          id?: string
          created_at?: string
          updated_at?: string
          first_name?: string
          last_name?: string
          email?: string | null
          phone?: string | null
          dog_name?: string | null
          other_dogs?: string[] | null
          address?: string | null
          active?: boolean
          membership?: boolean
        }
      }
      sessions: {
        Row: {
          id: string
          created_at: string
          updated_at: string
          client_id: string
          session_type: 'In-Person' | 'Online' | 'Training' | 'Online Catchup' | 'Group' | 'Phone Call' | 'Coaching'
          date: string
          time: string
          notes: string | null
          quote: number | null
          paid: boolean
          behavioral_plan: boolean
        }
        Insert: {
          id?: string
          created_at?: string
          updated_at?: string
          client_id: string
          session_type: 'In-Person' | 'Online' | 'Training' | 'Online Catchup' | 'Group' | 'Phone Call' | 'Coaching'
          date: string
          time: string
          notes?: string | null
          quote?: number | null
          paid?: boolean
          behavioral_plan?: boolean
        }
        Update: {
          id?: string
          created_at?: string
          updated_at?: string
          client_id?: string
          session_type?: 'In-Person' | 'Online' | 'Training' | 'Online Catchup' | 'Group' | 'Phone Call' | 'Coaching'
          date?: string
          time?: string
          notes?: string | null
          quote?: number | null
          paid?: boolean
          behavioral_plan?: boolean
        }
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      [_ in never]: never
    }
    Enums: {
      session_type: 'In-Person' | 'Online' | 'Training' | 'Online Catchup' | 'Group' | 'Phone Call' | 'Coaching'
    }
  }
}
