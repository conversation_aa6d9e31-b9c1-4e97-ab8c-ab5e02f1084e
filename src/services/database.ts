import { supabase } from '@/lib/supabase'
import { Client, Session } from '@/types'
import { Database } from '@/types/database'

type ClientRow = Database['public']['Tables']['clients']['Row']
type SessionRow = Database['public']['Tables']['sessions']['Row']
type ClientInsert = Database['public']['Tables']['clients']['Insert']
type SessionInsert = Database['public']['Tables']['sessions']['Insert']
type ClientUpdate = Database['public']['Tables']['clients']['Update']
type SessionUpdate = Database['public']['Tables']['sessions']['Update']

// Helper functions to convert between app types and database types
const dbClientToAppClient = (dbClient: ClientRow): Client => ({
  id: dbClient.id,
  firstName: dbClient.first_name,
  lastName: dbClient.last_name,
  email: dbClient.email || undefined,
  phone: dbClient.phone || undefined,
  dogName: dbClient.dog_name || undefined,
  otherDogs: dbClient.other_dogs || undefined,
  address: dbClient.address || undefined,
  active: dbClient.active,
  membership: dbClient.membership,
})

const appClientToDbClient = (client: Partial<Client>): ClientInsert | ClientUpdate => ({
  first_name: client.firstName!,
  last_name: client.lastName!,
  email: client.email || null,
  phone: client.phone || null,
  dog_name: client.dogName || null,
  other_dogs: client.otherDogs || null,
  address: client.address || null,
  active: client.active ?? true,
  membership: client.membership ?? false,
})

const dbSessionToAppSession = (dbSession: SessionRow & { clients?: ClientRow }): Session => ({
  id: dbSession.id,
  clientId: dbSession.client_id,
  sessionType: dbSession.session_type,
  date: dbSession.date,
  time: dbSession.time,
  notes: dbSession.notes || undefined,
  quote: dbSession.quote || undefined,
  paid: dbSession.paid,
  behavioralPlan: dbSession.behavioral_plan,
  // Include client data if joined
  client: dbSession.clients ? dbClientToAppClient(dbSession.clients) : undefined,
})

const appSessionToDbSession = (session: Partial<Session>): SessionInsert | SessionUpdate => ({
  client_id: session.clientId!,
  session_type: session.sessionType!,
  date: session.date!,
  time: session.time!,
  notes: session.notes || null,
  quote: session.quote || null,
  paid: session.paid ?? false,
  behavioral_plan: session.behavioralPlan ?? false,
})

// Client CRUD operations
export const clientService = {
  async getAll(): Promise<Client[]> {
    const { data, error } = await supabase
      .from('clients')
      .select('*')
      .order('created_at', { ascending: false })

    if (error) throw error
    return data.map(dbClientToAppClient)
  },

  async getById(id: string): Promise<Client | null> {
    const { data, error } = await supabase
      .from('clients')
      .select('*')
      .eq('id', id)
      .single()

    if (error) {
      if (error.code === 'PGRST116') return null // Not found
      throw error
    }
    return dbClientToAppClient(data)
  },

  async create(client: Omit<Client, 'id'>): Promise<Client> {
    const { data, error } = await supabase
      .from('clients')
      .insert(appClientToDbClient(client))
      .select()
      .single()

    if (error) throw error
    return dbClientToAppClient(data)
  },

  async update(id: string, updates: Partial<Client>): Promise<Client> {
    const { data, error } = await supabase
      .from('clients')
      .update(appClientToDbClient(updates))
      .eq('id', id)
      .select()
      .single()

    if (error) throw error
    return dbClientToAppClient(data)
  },

  async delete(id: string): Promise<void> {
    const { error } = await supabase
      .from('clients')
      .delete()
      .eq('id', id)

    if (error) throw error
  },
}

// Session CRUD operations
export const sessionService = {
  async getAll(): Promise<Session[]> {
    const { data, error } = await supabase
      .from('sessions')
      .select(`
        *,
        clients (*)
      `)
      .order('date', { ascending: false })
      .order('time', { ascending: false })

    if (error) throw error
    return data.map(dbSessionToAppSession)
  },

  async getById(id: string): Promise<Session | null> {
    const { data, error } = await supabase
      .from('sessions')
      .select(`
        *,
        clients (*)
      `)
      .eq('id', id)
      .single()

    if (error) {
      if (error.code === 'PGRST116') return null // Not found
      throw error
    }
    return dbSessionToAppSession(data)
  },

  async getByClientId(clientId: string): Promise<Session[]> {
    const { data, error } = await supabase
      .from('sessions')
      .select(`
        *,
        clients (*)
      `)
      .eq('client_id', clientId)
      .order('date', { ascending: false })
      .order('time', { ascending: false })

    if (error) throw error
    return data.map(dbSessionToAppSession)
  },

  async create(session: Omit<Session, 'id'>): Promise<Session> {
    const { data, error } = await supabase
      .from('sessions')
      .insert(appSessionToDbSession(session))
      .select(`
        *,
        clients (*)
      `)
      .single()

    if (error) throw error
    return dbSessionToAppSession(data)
  },

  async update(id: string, updates: Partial<Session>): Promise<Session> {
    const { data, error } = await supabase
      .from('sessions')
      .update(appSessionToDbSession(updates))
      .eq('id', id)
      .select(`
        *,
        clients (*)
      `)
      .single()

    if (error) throw error
    return dbSessionToAppSession(data)
  },

  async delete(id: string): Promise<void> {
    const { error } = await supabase
      .from('sessions')
      .delete()
      .eq('id', id)

    if (error) throw error
  },
}
