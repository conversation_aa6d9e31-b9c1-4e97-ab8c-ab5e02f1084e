'use client';

import { useState } from 'react';

interface BehavioralBriefForm {
  // Contact Information
  firstName: string;
  lastName: string;
  email: string;
  contactNumber: string;
  postcode: string;
  
  // Dog Information
  dogName: string;
  sex: 'Male' | 'Female' | '';
  breed: string;
  lifeWithDog: string;
  bestOutcome: string;
  sessionType: 'Online Session' | 'In-Person Session' | 'Rescue Remedy Session' | '';
}

export default function BehavioralBriefPage() {
  const [formData, setFormData] = useState<BehavioralBriefForm>({
    firstName: '',
    lastName: '',
    email: '',
    contactNumber: '',
    postcode: '',
    dogName: '',
    sex: '',
    breed: '',
    lifeWithDog: '',
    bestOutcome: '',
    sessionType: ''
  });

  const handleInputChange = (field: keyof BehavioralBriefForm, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    // TODO: Handle form submission and create new client if needed
    console.log('Form submitted:', formData);
    alert('Behavioural Brief submitted successfully!');
  };

  return (
    <div className="min-h-screen" style={{ backgroundColor: '#7FB069' }}>
      <div className="container mx-auto px-4 py-8 max-w-2xl">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold text-white mb-2">Behavioural Brief</h1>
        </div>

        {/* Form */}
        <form onSubmit={handleSubmit} className="space-y-8">
          {/* Contact Information Section */}
          <div className="space-y-6">
            <h2 className="text-lg font-semibold text-white tracking-wider">CONTACT INFORMATION</h2>
            
            {/* Owner Name */}
            <div className="space-y-4">
              <label className="block text-white font-medium">
                Owner Name <span className="text-red-300">*</span>
              </label>
              <div className="grid grid-cols-2 gap-4">
                <input
                  type="text"
                  placeholder="First Name"
                  value={formData.firstName}
                  onChange={(e) => handleInputChange('firstName', e.target.value)}
                  className="w-full px-4 py-3 rounded-lg border-2 border-transparent bg-white text-gray-900 placeholder-gray-500 focus:border-green-600 focus:ring-0 focus:outline-none transition-colors"
                  required
                />
                <input
                  type="text"
                  placeholder="Last Name"
                  value={formData.lastName}
                  onChange={(e) => handleInputChange('lastName', e.target.value)}
                  className="w-full px-4 py-3 rounded-lg border-2 border-transparent bg-white text-gray-900 placeholder-gray-500 focus:border-green-600 focus:ring-0 focus:outline-none transition-colors"
                  required
                />
              </div>
            </div>

            {/* Email */}
            <div className="space-y-2">
              <label className="block text-white font-medium">
                Email <span className="text-red-300">*</span>
              </label>
              <input
                type="email"
                value={formData.email}
                onChange={(e) => handleInputChange('email', e.target.value)}
                className="w-full px-4 py-3 rounded-lg border-2 border-transparent bg-white text-gray-900 placeholder-gray-500 focus:border-green-600 focus:ring-0 focus:outline-none transition-colors"
                required
              />
            </div>

            {/* Contact Number */}
            <div className="space-y-2">
              <label className="block text-white font-medium">
                Contact Number <span className="text-red-300">*</span>
              </label>
              <input
                type="tel"
                value={formData.contactNumber}
                onChange={(e) => handleInputChange('contactNumber', e.target.value)}
                className="w-full px-4 py-3 rounded-lg border-2 border-transparent bg-white text-gray-900 placeholder-gray-500 focus:border-green-600 focus:ring-0 focus:outline-none transition-colors"
                required
              />
            </div>

            {/* Postcode */}
            <div className="space-y-2">
              <label className="block text-white font-medium">
                Postcode <span className="text-red-300">*</span>
              </label>
              <input
                type="text"
                value={formData.postcode}
                onChange={(e) => handleInputChange('postcode', e.target.value)}
                className="w-full px-4 py-3 rounded-lg border-2 border-transparent bg-white text-gray-900 placeholder-gray-500 focus:border-green-600 focus:ring-0 focus:outline-none transition-colors"
                required
              />
            </div>
          </div>

          {/* Dog Information Section */}
          <div className="space-y-6">
            <div>
              <h2 className="text-lg font-semibold text-white tracking-wider">DOG INFORMATION</h2>
              <p className="text-white text-sm mt-1 opacity-90">
                If you are inquiring about more than one dog please complete an additional form.
              </p>
            </div>
            
            {/* Dog Name */}
            <div className="space-y-2">
              <label className="block text-white font-medium">
                Dog Name <span className="text-red-300">*</span>
              </label>
              <input
                type="text"
                value={formData.dogName}
                onChange={(e) => handleInputChange('dogName', e.target.value)}
                className="w-full px-4 py-3 rounded-lg border-2 border-transparent bg-white text-gray-900 placeholder-gray-500 focus:border-green-600 focus:ring-0 focus:outline-none transition-colors"
                required
              />
            </div>

            {/* Sex */}
            <div className="space-y-2">
              <label className="block text-white font-medium">
                Sex <span className="text-red-300">*</span>
              </label>
              <div className="flex gap-6">
                <label className="flex items-center text-white cursor-pointer">
                  <input
                    type="radio"
                    name="sex"
                    value="Male"
                    checked={formData.sex === 'Male'}
                    onChange={(e) => handleInputChange('sex', e.target.value)}
                    className="mr-2 w-4 h-4 text-green-600 border-2 border-white focus:ring-green-600 focus:ring-2"
                    required
                  />
                  Male
                </label>
                <label className="flex items-center text-white cursor-pointer">
                  <input
                    type="radio"
                    name="sex"
                    value="Female"
                    checked={formData.sex === 'Female'}
                    onChange={(e) => handleInputChange('sex', e.target.value)}
                    className="mr-2 w-4 h-4 text-green-600 border-2 border-white focus:ring-green-600 focus:ring-2"
                    required
                  />
                  Female
                </label>
              </div>
            </div>

            {/* Breed */}
            <div className="space-y-2">
              <label className="block text-white font-medium">
                What breed is your dog? <span className="text-red-300">*</span>
              </label>
              <p className="text-white text-sm opacity-90">Unknown/mixed is fine :-)</p>
              <input
                type="text"
                value={formData.breed}
                onChange={(e) => handleInputChange('breed', e.target.value)}
                className="w-full px-4 py-3 rounded-lg border-2 border-transparent bg-white text-gray-900 placeholder-gray-500 focus:border-green-600 focus:ring-0 focus:outline-none transition-colors"
                required
              />
            </div>

            {/* Life with Dog */}
            <div className="space-y-2">
              <label className="block text-white font-medium">
                In general, how is life with your dog, and what would you like help with? <span className="text-red-300">*</span>
              </label>
              <p className="text-white text-sm opacity-90">New puppy, new dog, new rescue, general training, behaviour concern, etc.</p>
              <textarea
                value={formData.lifeWithDog}
                onChange={(e) => handleInputChange('lifeWithDog', e.target.value)}
                rows={4}
                className="w-full px-4 py-3 rounded-lg border-2 border-transparent bg-white text-gray-900 placeholder-gray-500 focus:border-green-600 focus:ring-0 focus:outline-none transition-colors resize-none"
                required
              />
            </div>

            {/* Best Outcome */}
            <div className="space-y-2">
              <label className="block text-white font-medium">
                What would be the best outcome for you and your dog? <span className="text-red-300">*</span>
              </label>
              <p className="text-white text-sm opacity-90">E.g. a better relationship, a happier dog, an easier home life, more relaxed walks, etc.</p>
              <textarea
                value={formData.bestOutcome}
                onChange={(e) => handleInputChange('bestOutcome', e.target.value)}
                rows={4}
                className="w-full px-4 py-3 rounded-lg border-2 border-transparent bg-white text-gray-900 placeholder-gray-500 focus:border-green-600 focus:ring-0 focus:outline-none transition-colors resize-none"
                required
              />
            </div>

            {/* Session Type */}
            <div className="space-y-4">
              <label className="block text-white font-medium">
                Which type of session would you ideally like?
              </label>
              <div className="space-y-3">
                <label className="flex items-center text-white cursor-pointer">
                  <input
                    type="radio"
                    name="sessionType"
                    value="Online Session"
                    checked={formData.sessionType === 'Online Session'}
                    onChange={(e) => handleInputChange('sessionType', e.target.value)}
                    className="mr-3 w-4 h-4 text-green-600 border-2 border-white focus:ring-green-600 focus:ring-2"
                  />
                  Online Session
                </label>
                <label className="flex items-center text-white cursor-pointer">
                  <input
                    type="radio"
                    name="sessionType"
                    value="In-Person Session"
                    checked={formData.sessionType === 'In-Person Session'}
                    onChange={(e) => handleInputChange('sessionType', e.target.value)}
                    className="mr-3 w-4 h-4 text-green-600 border-2 border-white focus:ring-green-600 focus:ring-2"
                  />
                  In-Person Session
                </label>
                <label className="flex items-center text-white cursor-pointer">
                  <input
                    type="radio"
                    name="sessionType"
                    value="Rescue Remedy Session"
                    checked={formData.sessionType === 'Rescue Remedy Session'}
                    onChange={(e) => handleInputChange('sessionType', e.target.value)}
                    className="mr-3 w-4 h-4 text-green-600 border-2 border-white focus:ring-green-600 focus:ring-2"
                  />
                  Rescue Remedy Session (Dog Club members & current clients only)
                </label>
              </div>
            </div>
          </div>

          {/* Submit Button */}
          <div className="pt-6">
            <button
              type="submit"
              className="w-full bg-white text-green-700 font-semibold py-4 px-6 rounded-lg hover:bg-gray-50 focus:outline-none focus:ring-4 focus:ring-white focus:ring-opacity-50 transition-colors"
            >
              Submit Behavioural Brief
            </button>
          </div>
        </form>

        {/* Footer Text */}
        <div className="text-center mt-12">
          <h3 className="text-2xl font-bold text-white">A Happier Life with Your Dog</h3>
        </div>
      </div>
    </div>
  );
}
