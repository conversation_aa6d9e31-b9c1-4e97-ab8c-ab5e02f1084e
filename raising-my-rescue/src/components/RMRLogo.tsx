interface RMRLogoProps {
  size?: number;
  className?: string;
}

export default function RMRLogo({ size = 40, className = "" }: RMRLogoProps) {
  return (
    <div 
      className={`relative flex items-center justify-center ${className}`}
      style={{ width: size, height: size }}
    >
      {/* Background circle with green color */}
      <div 
        className="absolute inset-0 rounded-full"
        style={{ backgroundColor: '#6B7B5A' }}
      />
      
      {/* Paw print shape */}
      <svg 
        viewBox="0 0 100 100" 
        className="absolute inset-0 w-full h-full"
        style={{ transform: 'scale(0.8)' }}
      >
        {/* Main paw pad */}
        <ellipse 
          cx="50" 
          cy="65" 
          rx="18" 
          ry="22" 
          fill="#B85C3E"
        />
        
        {/* Top left toe */}
        <ellipse 
          cx="30" 
          cy="35" 
          rx="8" 
          ry="12" 
          fill="#B85C3E"
          transform="rotate(-15 30 35)"
        />
        
        {/* Top center toe */}
        <ellipse 
          cx="50" 
          cy="25" 
          rx="9" 
          ry="14" 
          fill="#B85C3E"
        />
        
        {/* Top right toe */}
        <ellipse 
          cx="70" 
          cy="35" 
          rx="8" 
          ry="12" 
          fill="#B85C3E"
          transform="rotate(15 70 35)"
        />
      </svg>
      
      {/* RMR Text */}
      <div className="absolute inset-0 flex items-center justify-center">
        <span 
          className="font-bold text-white"
          style={{ 
            fontSize: size * 0.25,
            letterSpacing: '0.05em'
          }}
        >
          RMR
        </span>
      </div>
    </div>
  );
}
