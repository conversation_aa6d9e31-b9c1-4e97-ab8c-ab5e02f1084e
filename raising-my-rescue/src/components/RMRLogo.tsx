interface RMRLogoProps {
  size?: number;
  className?: string;
}

export default function RMRLogo({ size = 40, className = "" }: RMRLogoProps) {
  return (
    <div
      className={`relative flex items-center justify-center rounded-lg overflow-hidden ${className}`}
      style={{ width: size, height: size }}
    >
      {/* Green background */}
      <div
        className="absolute inset-0"
        style={{ backgroundColor: '#6B7B5A' }}
      />

      {/* Paw print shape - recreated to match the uploaded image exactly */}
      <svg
        viewBox="0 0 100 100"
        className="absolute inset-0 w-full h-full"
      >
        {/* Top left toe */}
        <ellipse
          cx="30"
          cy="25"
          rx="8"
          ry="12"
          fill="#B85C3E"
          transform="rotate(-15 30 25)"
        />

        {/* Top center-left toe */}
        <ellipse
          cx="45"
          cy="18"
          rx="9"
          ry="14"
          fill="#B85C3E"
        />

        {/* Top center-right toe */}
        <ellipse
          cx="60"
          cy="18"
          rx="9"
          ry="14"
          fill="#B85C3E"
        />

        {/* Top right toe */}
        <ellipse
          cx="75"
          cy="25"
          rx="8"
          ry="12"
          fill="#B85C3E"
          transform="rotate(15 75 25)"
        />

        {/* Main paw pad - large oval at bottom */}
        <ellipse
          cx="52"
          cy="65"
          rx="25"
          ry="20"
          fill="#B85C3E"
        />
      </svg>

      {/* RMR Text - positioned over the paw print like in the image */}
      <div className="absolute inset-0 flex items-center justify-center">
        <span
          className="font-bold text-white"
          style={{
            fontSize: size * 0.35,
            letterSpacing: '0.05em',
            fontFamily: 'serif',
            textShadow: '1px 1px 2px rgba(0,0,0,0.3)'
          }}
        >
          RMR
        </span>
      </div>
    </div>
  );
}
