interface RMRLogoProps {
  size?: number;
  className?: string;
}

export default function RMRLogo({ size = 40, className = "" }: RMRLogoProps) {
  return (
    <div
      className={`relative flex items-center justify-center rounded-lg overflow-hidden ${className}`}
      style={{ width: size, height: size }}
    >
      {/* Green background */}
      <div
        className="absolute inset-0"
        style={{ backgroundColor: '#6B7B5A' }}
      />

      {/* Paw print shape - positioned to match the uploaded image */}
      <svg
        viewBox="0 0 100 100"
        className="absolute inset-0 w-full h-full"
        style={{ transform: 'scale(1.2) translate(-5px, 5px)' }}
      >
        {/* Main paw pad - larger and more oval */}
        <ellipse
          cx="50"
          cy="70"
          rx="22"
          ry="25"
          fill="#B85C3E"
        />

        {/* Top left toe - larger */}
        <ellipse
          cx="25"
          cy="30"
          rx="12"
          ry="18"
          fill="#B85C3E"
          transform="rotate(-20 25 30)"
        />

        {/* Top center toe - larger */}
        <ellipse
          cx="50"
          cy="20"
          rx="13"
          ry="20"
          fill="#B85C3E"
        />

        {/* Top right toe - larger */}
        <ellipse
          cx="75"
          cy="30"
          rx="12"
          ry="18"
          fill="#B85C3E"
          transform="rotate(20 75 30)"
        />
      </svg>

      {/* RMR Text - positioned to match the uploaded image */}
      <div className="absolute inset-0 flex items-center justify-center" style={{ transform: 'translateY(-2px)' }}>
        <span
          className="font-bold text-white"
          style={{
            fontSize: size * 0.28,
            letterSpacing: '0.1em',
            fontFamily: 'serif'
          }}
        >
          RMR
        </span>
      </div>
    </div>
  );
}
