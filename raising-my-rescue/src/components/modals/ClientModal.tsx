'use client';

import { Client } from '@/types';
import { useApp } from '@/context/AppContext';
import SlideUpModal from './SlideUpModal';

interface ClientModalProps {
  client: Client | null;
  isOpen: boolean;
  onClose: () => void;
  onEditClient: (client: Client) => void;
}

export default function ClientModal({ client, isOpen, onClose, onEditClient }: ClientModalProps) {
  const { dispatch } = useApp();

  if (!client) return null;

  const handleDelete = () => {
    // Show confirmation dialog
    if (window.confirm('Are you sure you want to delete this client? This action cannot be undone.')) {
      dispatch({ type: 'DELETE_CLIENT', payload: client.id });
      onClose();
    }
  };

  const handleToggleActive = () => {
    dispatch({
      type: 'UPDATE_CLIENT',
      payload: { ...client, active: !client.active }
    });
  };

  const handleEditClick = () => {
    onEditClient(client);
    onClose();
  };

  const getAvatarText = (firstName: string, lastName: string) => {
    return `${firstName?.[0] || ''}${lastName?.[0] || ''}`.toUpperCase();
  };

  return (
    <SlideUpModal
      isOpen={isOpen}
      onClose={onClose}
      title={`${client.firstName} ${client.lastName}`}
    >
      <div className="space-y-6">
        {/* Action Buttons */}
        <div className="flex gap-3">
          <button
            onClick={handleEditClick}
            className="flex-1 bg-amber-800 hover:bg-amber-700 text-white py-3 px-4 rounded-lg font-medium transition-colors"
          >
            Edit Client
          </button>
        </div>

        {/* Client Details */}

        <div className="space-y-4">
          {client.email && (
            <div className="flex justify-between items-center">
              <span className="text-gray-600">Email</span>
              <span className="font-medium text-gray-900">{client.email}</span>
            </div>
          )}

          {client.phone && (
            <div className="flex justify-between items-center">
              <span className="text-gray-600">Phone</span>
              <span className="font-medium text-gray-900">{client.phone}</span>
            </div>
          )}

          {client.dogName && (
            <div className="flex justify-between items-center">
              <span className="text-gray-600">Dog Name</span>
              <span className="font-medium text-gray-900">{client.dogName}</span>
            </div>
          )}

          {client.otherDogs && client.otherDogs.length > 0 && (
            <div className="flex justify-between items-center">
              <span className="text-gray-600">Other Dogs</span>
              <span className="font-medium text-gray-900">{client.otherDogs.join(', ')}</span>
            </div>
          )}

          {client.address && (
            <div className="space-y-1">
              <span className="text-gray-600">Address</span>
              <div className="font-medium text-gray-900 text-right">{client.address}</div>
            </div>
          )}

          {/* Toggle Switch */}
          <div className="flex justify-between items-center">
            <span className="text-gray-700 font-medium">Active Client</span>
            <button
              onClick={handleToggleActive}
              className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                client.active ? 'bg-custom-brown-600' : 'bg-gray-300'
              }`}
            >
              <span
                className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                  client.active ? 'translate-x-6' : 'translate-x-1'
                }`}
              />
            </button>
          </div>
        </div>

        {/* Delete Button */}
        <button
          onClick={handleDelete}
          className="w-full bg-custom-brown-800 hover:bg-custom-brown-700 text-white py-3 px-4 rounded-lg font-medium transition-colors mt-6"
        >
          Delete Client
        </button>
      </div>
    </SlideUpModal>
  );
}
