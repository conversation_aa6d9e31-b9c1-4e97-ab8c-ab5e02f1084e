'use client';

import { Client } from '@/types';
import { useApp } from '@/context/AppContext';
import SlideUpModal from './SlideUpModal';
import { Edit, Trash2 } from 'lucide-react';

interface ClientModalProps {
  client: Client | null;
  isOpen: boolean;
  onClose: () => void;
  onEditClient: (client: Client) => void;
}

export default function ClientModal({ client, isOpen, onClose, onEditClient }: ClientModalProps) {
  const { dispatch } = useApp();

  if (!client) return null;

  const handleDelete = () => {
    dispatch({ type: 'DELETE_CLIENT', payload: client.id });
    onClose();
  };

  const handleToggleActive = () => {
    dispatch({
      type: 'UPDATE_CLIENT',
      payload: { ...client, active: !client.active }
    });
  };

  const handleEditClick = () => {
    onEditClient(client);
    onClose();
  };

  const getAvatarText = (firstName: string, lastName: string) => {
    return `${firstName?.[0] || ''}${lastName?.[0] || ''}`.toUpperCase();
  };

  return (
    <SlideUpModal
      isOpen={isOpen}
      onClose={onClose}
      title={`${client.firstName} ${client.lastName}`}
    >
      <div className="space-y-6">
        {/* Client Details */}

        <div className="space-y-4">
          {client.email && (
            <div className="flex justify-between items-center">
              <span className="text-gray-600">Email</span>
              <span className="font-medium text-gray-900">{client.email}</span>
            </div>
          )}

          {client.phone && (
            <div className="flex justify-between items-center">
              <span className="text-gray-600">Phone</span>
              <span className="font-medium text-gray-900">{client.phone}</span>
            </div>
          )}

          {client.dogName && (
            <div className="flex justify-between items-center">
              <span className="text-gray-600">Dog Name</span>
              <span className="font-medium text-gray-900">{client.dogName}</span>
            </div>
          )}

          {client.otherDogs && client.otherDogs.length > 0 && (
            <div className="flex justify-between items-center">
              <span className="text-gray-600">Other Dogs</span>
              <span className="font-medium text-gray-900">{client.otherDogs.join(', ')}</span>
            </div>
          )}

          {client.address && (
            <div className="flex justify-between items-center">
              <span className="text-gray-600">Address</span>
              <span className="font-medium text-gray-900">{client.address}</span>
            </div>
          )}

          {/* Toggle Switch */}
          <div className="flex justify-between items-center">
            <span className="text-gray-700 font-medium">Active Client</span>
            <button
              onClick={handleToggleActive}
              className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                client.active ? 'bg-amber-600' : 'bg-gray-300'
              }`}
            >
              <span
                className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                  client.active ? 'translate-x-6' : 'translate-x-1'
                }`}
              />
            </button>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex gap-3 justify-center">
          <button
            onClick={handleEditClick}
            className="w-12 h-12 bg-amber-800 hover:bg-amber-700 text-white rounded-lg transition-colors flex items-center justify-center"
          >
            <Edit size={20} />
          </button>
          <button
            onClick={handleDelete}
            className="w-12 h-12 bg-red-600 hover:bg-red-700 text-white rounded-lg transition-colors flex items-center justify-center"
          >
            <Trash2 size={20} />
          </button>
        </div>
      </div>
    </SlideUpModal>
  );
}
