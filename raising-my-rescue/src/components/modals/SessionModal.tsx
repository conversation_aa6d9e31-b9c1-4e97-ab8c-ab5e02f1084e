'use client';

import { Session } from '@/types';
import { useApp } from '@/context/AppContext';
import SlideUpModal from './SlideUpModal';
import { format } from 'date-fns';

interface SessionModalProps {
  session: Session | null;
  isOpen: boolean;
  onClose: () => void;
}

export default function SessionModal({ session, isOpen, onClose }: SessionModalProps) {
  const { dispatch } = useApp();

  if (!session) return null;

  const handleDelete = () => {
    dispatch({ type: 'DELETE_SESSION', payload: session.id });
    onClose();
  };

  const handleTogglePaid = () => {
    dispatch({
      type: 'UPDATE_SESSION',
      payload: { ...session, paid: !session.paid }
    });
  };

  const handleToggleBehaviourPlan = () => {
    dispatch({
      type: 'UPDATE_SESSION',
      payload: { ...session, behaviourPlanSent: !session.behaviourPlanSent }
    });
  };

  const displayName = session.dogName 
    ? `${session.ownerName} w/ ${session.dogName}`
    : session.ownerName;

  return (
    <SlideUpModal
      isOpen={isOpen}
      onClose={onClose}
      title={displayName}
    >
      <div className="space-y-6">
        {/* Action Buttons */}
        <div className="flex gap-3">
          <button className="flex-1 bg-gray-100 hover:bg-gray-200 py-3 px-4 rounded-lg font-medium transition-colors text-black">
            Edit Session
          </button>
          <button className="flex-1 bg-gray-100 hover:bg-gray-200 py-3 px-4 rounded-lg font-medium transition-colors text-black">
            Client Profile
          </button>
        </div>

        {/* Session Details */}
        <div className="space-y-4">
          <div className="flex justify-between items-center">
            <span className="text-gray-600">Owner(s) Name</span>
            <span className="font-medium text-black">{session.ownerName}</span>
          </div>

          {session.dogName && (
            <div className="flex justify-between items-center">
              <span className="text-gray-600">Dog(s) Name</span>
              <span className="font-medium text-black">{session.dogName}</span>
            </div>
          )}

          <div className="flex justify-between items-center">
            <span className="text-gray-600">Booking</span>
            <span className="font-medium text-black">
              {format(session.bookingDate, 'dd/MM/yyyy, HH:mm')}
            </span>
          </div>

          <div className="flex justify-between items-center">
            <span className="text-gray-600">Session Type</span>
            <span className="font-medium text-black">{session.sessionType}</span>
          </div>

          <div className="flex justify-between items-center">
            <span className="text-gray-600">Quote</span>
            <span className="font-medium text-black">£{session.quote}</span>
          </div>

          {/* Toggle Switches */}
          <div className="flex justify-between items-center">
            <span className="text-gray-600 font-medium">Paid</span>
            <button
              onClick={handleTogglePaid}
              className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                session.paid ? 'bg-green-500' : 'bg-gray-300'
              }`}
            >
              <span
                className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                  session.paid ? 'translate-x-6' : 'translate-x-1'
                }`}
              />
            </button>
          </div>

          <div className="flex justify-between items-center">
            <span className="text-gray-600 font-medium">Behaviour Plan Sent</span>
            <button
              onClick={handleToggleBehaviourPlan}
              className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                session.behaviourPlanSent ? 'bg-green-500' : 'bg-gray-300'
              }`}
            >
              <span
                className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                  session.behaviourPlanSent ? 'translate-x-6' : 'translate-x-1'
                }`}
              />
            </button>
          </div>
        </div>

        {/* Delete Button */}
        <button
          onClick={handleDelete}
          className="w-full bg-red-600 hover:bg-red-700 py-3 px-4 rounded-lg font-medium transition-colors mt-6"
        >
          Delete Session
        </button>
      </div>
    </SlideUpModal>
  );
}
