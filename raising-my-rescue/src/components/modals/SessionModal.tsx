'use client';

import { Session } from '@/types';
import { useApp } from '@/context/AppContext';
import SlideUpModal from './SlideUpModal';
import { format } from 'date-fns';
import { Edit, User, Trash2 } from 'lucide-react';

interface SessionModalProps {
  session: Session | null;
  isOpen: boolean;
  onClose: () => void;
  onEditSession: (session: Session) => void;
  onEditClient: (session: Session) => void;
}

export default function SessionModal({ session, isOpen, onClose, onEditSession, onEditClient }: SessionModalProps) {
  const { dispatch, state } = useApp();

  if (!session) return null;

  // Find the client for this session
  const client = state.clients.find(c => c.id === session.clientId);

  const handleDelete = () => {
    dispatch({ type: 'DELETE_SESSION', payload: session.id });
    onClose();
  };



  const displayName = client
    ? `${client.firstName} ${client.lastName}${client.dogName ? ` w/ ${client.dogName}` : ''}`
    : 'Unknown Client';

  return (
    <SlideUpModal
      isOpen={isOpen}
      onClose={onClose}
      title={displayName}
    >
      <div className="space-y-6">
        {/* Session Details */}
        <div className="space-y-4">
          {client && (
            <>
              <div className="flex justify-between items-center">
                <span className="text-gray-600">Owner(s) Name</span>
                <span className="font-medium text-gray-900">{client.firstName} {client.lastName}</span>
              </div>

              {client.dogName && (
                <div className="flex justify-between items-center">
                  <span className="text-gray-600">Dog(s) Name</span>
                  <span className="font-medium text-gray-900">{client.dogName}</span>
                </div>
              )}
            </>
          )}

          <div className="flex justify-between items-center">
            <span className="text-gray-600">Booking</span>
            <span className="font-medium text-gray-900">
              {format(session.bookingDate, 'dd/MM/yyyy, HH:mm')}
            </span>
          </div>

          <div className="flex justify-between items-center">
            <span className="text-gray-600">Session Type</span>
            <span className="font-medium text-gray-900">{session.sessionType}</span>
          </div>

          <div className="flex justify-between items-center">
            <span className="text-gray-600">Quote</span>
            <span className="font-medium text-gray-900">£{session.quote}</span>
          </div>

          {session.notes && (
            <div className="flex justify-between items-start">
              <span className="text-gray-600">Notes</span>
              <span className="font-medium text-gray-900 text-right max-w-48">{session.notes}</span>
            </div>
          )}

        </div>

        {/* Action Buttons */}
        <div className="flex gap-3 justify-center">
          <button
            onClick={() => onEditSession(session)}
            className="w-12 h-12 bg-amber-800 hover:bg-amber-700 text-white rounded-lg transition-colors flex items-center justify-center"
          >
            <Edit size={20} />
          </button>
          <button
            onClick={() => onEditClient(session)}
            className="w-12 h-12 bg-amber-800 hover:bg-amber-700 text-white rounded-lg transition-colors flex items-center justify-center"
          >
            <User size={20} />
          </button>
          <button
            onClick={handleDelete}
            className="w-12 h-12 bg-red-600 hover:bg-red-700 text-white rounded-lg transition-colors flex items-center justify-center"
          >
            <Trash2 size={20} />
          </button>
        </div>
      </div>
    </SlideUpModal>
  );
}
