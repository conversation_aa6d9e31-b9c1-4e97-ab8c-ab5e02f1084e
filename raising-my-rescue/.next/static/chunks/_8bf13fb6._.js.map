{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/RMR%20App%20Version%202/raising-my-rescue/src/components/layout/Header.tsx"], "sourcesContent": ["'use client';\n\nimport { Search, Plus, MoreHorizontal } from 'lucide-react';\nimport { useState } from 'react';\n\ninterface HeaderProps {\n  title: string;\n  showAddButton?: boolean;\n  onAddClick?: () => void;\n  addButtonText?: string;\n  showSearch?: boolean;\n  onSearch?: (query: string) => void;\n  searchPlaceholder?: string;\n}\n\nexport default function Header({\n  title,\n  showAddButton = false,\n  onAddClick,\n  addButtonText = 'Add',\n  showSearch = false,\n  onSearch,\n  searchPlaceholder = 'Search',\n}: HeaderProps) {\n  const [searchQuery, setSearchQuery] = useState('');\n\n  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {\n    const query = e.target.value;\n    setSearchQuery(query);\n    onSearch?.(query);\n  };\n\n  return (\n    <div className=\"bg-amber-800 text-white px-4 py-4 safe-area-pt\">\n      {/* Top row with title and buttons */}\n      <div className=\"flex items-center justify-between mb-4\">\n        <h1 className=\"text-xl font-semibold\">{title}</h1>\n        <div className=\"flex items-center gap-2\">\n          {showAddButton && (\n            <button\n              onClick={onAddClick}\n              className=\"bg-white/20 hover:bg-white/30 px-4 py-2 rounded-full text-sm font-medium transition-colors\"\n            >\n              {addButtonText}\n            </button>\n          )}\n          <button className=\"p-2 hover:bg-white/20 rounded-full transition-colors\">\n            <MoreHorizontal size={20} />\n          </button>\n        </div>\n      </div>\n\n      {/* Search bar */}\n      {showSearch && (\n        <div className=\"relative\">\n          <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-white/60\" size={20} />\n          <input\n            type=\"text\"\n            value={searchQuery}\n            onChange={handleSearchChange}\n            placeholder={searchPlaceholder}\n            className=\"w-full bg-white/20 placeholder-white/60 text-white px-10 py-3 rounded-lg focus:outline-none focus:bg-white/30 transition-colors\"\n          />\n        </div>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AACA;;;AAHA;;;AAee,SAAS,OAAO,EAC7B,KAAK,EACL,gBAAgB,KAAK,EACrB,UAAU,EACV,gBAAgB,KAAK,EACrB,aAAa,KAAK,EAClB,QAAQ,EACR,oBAAoB,QAAQ,EAChB;;IACZ,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,MAAM,qBAAqB,CAAC;QAC1B,MAAM,QAAQ,EAAE,MAAM,CAAC,KAAK;QAC5B,eAAe;QACf,WAAW;IACb;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAyB;;;;;;kCACvC,6LAAC;wBAAI,WAAU;;4BACZ,+BACC,6LAAC;gCACC,SAAS;gCACT,WAAU;0CAET;;;;;;0CAGL,6LAAC;gCAAO,WAAU;0CAChB,cAAA,6LAAC,mNAAA,CAAA,iBAAc;oCAAC,MAAM;;;;;;;;;;;;;;;;;;;;;;;YAM3B,4BACC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,yMAAA,CAAA,SAAM;wBAAC,WAAU;wBAAmE,MAAM;;;;;;kCAC3F,6LAAC;wBACC,MAAK;wBACL,OAAO;wBACP,UAAU;wBACV,aAAa;wBACb,WAAU;;;;;;;;;;;;;;;;;;AAMtB;GApDwB;KAAA", "debugId": null}}, {"offset": {"line": 127, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/RMR%20App%20Version%202/raising-my-rescue/src/app/clients/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { useApp } from '@/context/AppContext';\nimport Header from '@/components/layout/Header';\nimport { Client } from '@/types';\nimport { MoreHorizontal } from 'lucide-react';\n\nexport default function ClientsPage() {\n  const { state } = useApp();\n  const [searchQuery, setSearchQuery] = useState('');\n\n  const filteredClients = state.clients.filter(client => {\n    const searchTerm = searchQuery.toLowerCase();\n    return (\n      client.ownerName.toLowerCase().includes(searchTerm) ||\n      client.dogName.toLowerCase().includes(searchTerm)\n    );\n  });\n\n  const activeClients = filteredClients.filter(client => client.active);\n\n  const getAvatarText = (ownerName: string) => {\n    return ownerName.split(' ').map(name => name[0]).join('').toUpperCase();\n  };\n\n  const handleClientClick = (client: Client) => {\n    // TODO: Implement client modal or navigation\n    console.log('Client clicked:', client);\n  };\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      <Header\n        title={`${activeClients.length} Active`}\n        showAddButton\n        addButtonText=\"Add Client\"\n        onAddClick={() => {/* TODO: Implement add client */}}\n        showSearch\n        onSearch={setSearchQuery}\n        searchPlaceholder=\"Search\"\n      />\n\n      <div className=\"px-4 py-4\">\n        {/* Clients List */}\n        <div className=\"space-y-3\">\n          {activeClients.map((client) => (\n            <div\n              key={client.id}\n              onClick={() => handleClientClick(client)}\n              className=\"bg-white rounded-lg p-4 shadow-sm flex items-center justify-between active:bg-gray-50 transition-colors cursor-pointer\"\n            >\n              <div className=\"flex items-center space-x-3\">\n                <div className=\"w-10 h-10 bg-amber-800 rounded-full flex items-center justify-center text-white text-sm font-medium\">\n                  {client.avatar || getAvatarText(client.ownerName)}\n                </div>\n                <div>\n                  <h3 className=\"font-medium text-gray-900\">{client.ownerName}</h3>\n                  {client.dogName && (\n                    <p className=\"text-sm text-gray-500\">{client.dogName}</p>\n                  )}\n                </div>\n              </div>\n              <button className=\"p-2 hover:bg-gray-100 rounded-full transition-colors\">\n                <MoreHorizontal size={20} className=\"text-gray-400\" />\n              </button>\n            </div>\n          ))}\n        </div>\n\n        {activeClients.length === 0 && (\n          <div className=\"text-center py-12\">\n            <p className=\"text-gray-500\">No clients found</p>\n          </div>\n        )}\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAEA;;;AANA;;;;;AAQe,SAAS;;IACtB,MAAM,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,gIAAA,CAAA,SAAM,AAAD;IACvB,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,MAAM,kBAAkB,MAAM,OAAO,CAAC,MAAM,CAAC,CAAA;QAC3C,MAAM,aAAa,YAAY,WAAW;QAC1C,OACE,OAAO,SAAS,CAAC,WAAW,GAAG,QAAQ,CAAC,eACxC,OAAO,OAAO,CAAC,WAAW,GAAG,QAAQ,CAAC;IAE1C;IAEA,MAAM,gBAAgB,gBAAgB,MAAM,CAAC,CAAA,SAAU,OAAO,MAAM;IAEpE,MAAM,gBAAgB,CAAC;QACrB,OAAO,UAAU,KAAK,CAAC,KAAK,GAAG,CAAC,CAAA,OAAQ,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,IAAI,WAAW;IACvE;IAEA,MAAM,oBAAoB,CAAC;QACzB,6CAA6C;QAC7C,QAAQ,GAAG,CAAC,mBAAmB;IACjC;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC,yIAAA,CAAA,UAAM;gBACL,OAAO,GAAG,cAAc,MAAM,CAAC,OAAO,CAAC;gBACvC,aAAa;gBACb,eAAc;gBACd,YAAY,KAAuC;gBACnD,UAAU;gBACV,UAAU;gBACV,mBAAkB;;;;;;0BAGpB,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;kCACZ,cAAc,GAAG,CAAC,CAAC,uBAClB,6LAAC;gCAEC,SAAS,IAAM,kBAAkB;gCACjC,WAAU;;kDAEV,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACZ,OAAO,MAAM,IAAI,cAAc,OAAO,SAAS;;;;;;0DAElD,6LAAC;;kEACC,6LAAC;wDAAG,WAAU;kEAA6B,OAAO,SAAS;;;;;;oDAC1D,OAAO,OAAO,kBACb,6LAAC;wDAAE,WAAU;kEAAyB,OAAO,OAAO;;;;;;;;;;;;;;;;;;kDAI1D,6LAAC;wCAAO,WAAU;kDAChB,cAAA,6LAAC,mNAAA,CAAA,iBAAc;4CAAC,MAAM;4CAAI,WAAU;;;;;;;;;;;;+BAhBjC,OAAO,EAAE;;;;;;;;;;oBAsBnB,cAAc,MAAM,KAAK,mBACxB,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAE,WAAU;sCAAgB;;;;;;;;;;;;;;;;;;;;;;;AAMzC;GAtEwB;;QACJ,gIAAA,CAAA,SAAM;;;KADF", "debugId": null}}, {"offset": {"line": 295, "column": 0}, "map": {"version": 3, "file": "search.js", "sources": ["file:///Users/<USER>/Downloads/RMR%20App%20Version%202/raising-my-rescue/node_modules/lucide-react/src/icons/search.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'm21 21-4.34-4.34', key: '14j7rj' }],\n  ['circle', { cx: '11', cy: '11', r: '8', key: '4ej97u' }],\n];\n\n/**\n * @component @name Search\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJtMjEgMjEtNC4zNC00LjM0IiAvPgogIDxjaXJjbGUgY3g9IjExIiBjeT0iMTEiIHI9IjgiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/search\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Search = createLucideIcon('search', __iconNode);\n\nexport default Search;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAoB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IACjD;QAAC,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA;YAAE,CAAI,CAAA,CAAA,CAAA,IAAA,CAAM;YAAA,CAAA,CAAI,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAM,CAAG,CAAA,CAAA,CAAA,CAAA,CAAA;YAAK,CAAK,CAAA,CAAA,CAAA,CAAA,QAAA;QAAU,CAAA;KAAA;CAC1D;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,MAAA,CAAS,CAAA,2KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAU,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 343, "column": 0}, "map": {"version": 3, "file": "ellipsis.js", "sources": ["file:///Users/<USER>/Downloads/RMR%20App%20Version%202/raising-my-rescue/node_modules/lucide-react/src/icons/ellipsis.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['circle', { cx: '12', cy: '12', r: '1', key: '41hilf' }],\n  ['circle', { cx: '19', cy: '12', r: '1', key: '1wjl8i' }],\n  ['circle', { cx: '5', cy: '12', r: '1', key: '1pcz8c' }],\n];\n\n/**\n * @component @name Ellipsis\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8Y2lyY2xlIGN4PSIxMiIgY3k9IjEyIiByPSIxIiAvPgogIDxjaXJjbGUgY3g9IjE5IiBjeT0iMTIiIHI9IjEiIC8+CiAgPGNpcmNsZSBjeD0iNSIgY3k9IjEyIiByPSIxIiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/ellipsis\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Ellipsis = createLucideIcon('ellipsis', __iconNode);\n\nexport default Ellipsis;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,QAAU,CAAA;QAAA,CAAA;YAAE,EAAI,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAM,CAAI,CAAA,CAAA,CAAA,IAAA,CAAM;YAAA,CAAG,EAAA,CAAA,CAAA,CAAA,CAAA;YAAK,GAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAU;KAAA;IACxD;QAAC,QAAU,CAAA;QAAA,CAAA;YAAE,EAAI,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAM,CAAI,CAAA,CAAA,CAAA,IAAA,CAAM;YAAA,CAAG,EAAA,CAAA,CAAA,CAAA,CAAA;YAAK,GAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAU;KAAA;IACxD;QAAC,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA;YAAE,CAAI,CAAA,CAAA,CAAA,GAAA,CAAK;YAAA,CAAA,CAAI,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAM,CAAG,CAAA,CAAA,CAAA,CAAA,CAAA;YAAK,CAAK,CAAA,CAAA,CAAA,CAAA,QAAA;QAAU,CAAA;KAAA;CACzD;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,QAAA,CAAW,CAAA,2KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAiB,AAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAY,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}]}