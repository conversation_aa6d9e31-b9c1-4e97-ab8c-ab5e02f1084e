{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/RMR%20App%20Version%202/raising-my-rescue/src/app/behavioral-brief/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\n\nexport default function BehavioralBriefPage() {\n  return (\n    <div className=\"min-h-screen\" style={{ backgroundColor: '#7FB069' }}>\n      <div className=\"container mx-auto px-4 py-8 max-w-2xl\">\n        <div className=\"text-center mb-8\">\n          <h1 className=\"text-4xl font-bold text-white mb-2\">Behavioural Brief</h1>\n        </div>\n        <p className=\"text-white text-center\">Form coming soon...</p>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;;AAIe,SAAS;IACtB,qBACE,6LAAC;QAAI,WAAU;QAAe,OAAO;YAAE,iBAAiB;QAAU;kBAChE,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAG,WAAU;kCAAqC;;;;;;;;;;;8BAErD,6LAAC;oBAAE,WAAU;8BAAyB;;;;;;;;;;;;;;;;;AAI9C;KAXwB", "debugId": null}}]}