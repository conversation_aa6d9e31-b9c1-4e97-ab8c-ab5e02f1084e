{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/RMR%20App%20Version%202/raising-my-rescue/src/components/layout/Header.tsx"], "sourcesContent": ["'use client';\n\nimport { Search, Plus, MoreHorizontal } from 'lucide-react';\nimport { useState } from 'react';\n\ninterface HeaderProps {\n  title: string;\n  showAddButton?: boolean;\n  onAddClick?: () => void;\n  addButtonText?: string;\n  showSearch?: boolean;\n  onSearch?: (query: string) => void;\n  searchPlaceholder?: string;\n}\n\nexport default function Header({\n  title,\n  showAddButton = false,\n  onAddClick,\n  addButtonText = 'Add',\n  showSearch = false,\n  onSearch,\n  searchPlaceholder = 'Search',\n}: HeaderProps) {\n  const [searchQuery, setSearchQuery] = useState('');\n\n  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {\n    const query = e.target.value;\n    setSearchQuery(query);\n    onSearch?.(query);\n  };\n\n  return (\n    <div className=\"bg-amber-800 text-white px-4 py-4 safe-area-pt\">\n      {/* Top row with title and buttons */}\n      <div className=\"flex items-center justify-between mb-4\">\n        <h1 className=\"text-xl font-semibold\">{title}</h1>\n        <div className=\"flex items-center gap-2\">\n          {showAddButton && (\n            <button\n              onClick={onAddClick}\n              className=\"bg-white/20 hover:bg-white/30 px-4 py-2 rounded-full text-sm font-medium transition-colors\"\n            >\n              {addButtonText}\n            </button>\n          )}\n          <button className=\"p-2 hover:bg-white/20 rounded-full transition-colors\">\n            <MoreHorizontal size={20} />\n          </button>\n        </div>\n      </div>\n\n      {/* Search bar */}\n      {showSearch && (\n        <div className=\"relative\">\n          <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-white/60\" size={20} />\n          <input\n            type=\"text\"\n            value={searchQuery}\n            onChange={handleSearchChange}\n            placeholder={searchPlaceholder}\n            className=\"w-full bg-white/20 placeholder-white/60 text-white px-10 py-3 rounded-lg focus:outline-none focus:bg-white/30 transition-colors\"\n          />\n        </div>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AACA;AAHA;;;;AAee,SAAS,OAAO,EAC7B,KAAK,EACL,gBAAgB,KAAK,EACrB,UAAU,EACV,gBAAgB,KAAK,EACrB,aAAa,KAAK,EAClB,QAAQ,EACR,oBAAoB,QAAQ,EAChB;IACZ,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,MAAM,qBAAqB,CAAC;QAC1B,MAAM,QAAQ,EAAE,MAAM,CAAC,KAAK;QAC5B,eAAe;QACf,WAAW;IACb;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAyB;;;;;;kCACvC,8OAAC;wBAAI,WAAU;;4BACZ,+BACC,8OAAC;gCACC,SAAS;gCACT,WAAU;0CAET;;;;;;0CAGL,8OAAC;gCAAO,WAAU;0CAChB,cAAA,8OAAC,gNAAA,CAAA,iBAAc;oCAAC,MAAM;;;;;;;;;;;;;;;;;;;;;;;YAM3B,4BACC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,sMAAA,CAAA,SAAM;wBAAC,WAAU;wBAAmE,MAAM;;;;;;kCAC3F,8OAAC;wBACC,MAAK;wBACL,OAAO;wBACP,UAAU;wBACV,aAAa;wBACb,WAAU;;;;;;;;;;;;;;;;;;AAMtB", "debugId": null}}, {"offset": {"line": 118, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/RMR%20App%20Version%202/raising-my-rescue/src/app/finance/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useApp } from '@/context/AppContext';\nimport Header from '@/components/layout/Header';\n\nexport default function FinancePage() {\n  const { state } = useApp();\n\n  // Calculate total finances\n  const totalFinances = state.finances.reduce((total, finance) => total + finance.actual, 0);\n  \n  // Group finances by year\n  const financesByYear = state.finances.reduce((acc, finance) => {\n    const year = finance.year;\n    if (!acc[year]) {\n      acc[year] = [];\n    }\n    acc[year].push(finance);\n    return acc;\n  }, {} as Record<number, typeof state.finances>);\n\n  // Calculate year totals\n  const yearTotals = Object.entries(financesByYear).map(([year, finances]) => ({\n    year: parseInt(year),\n    total: finances.reduce((sum, f) => sum + f.actual, 0),\n    finances: finances.sort((a, b) => {\n      const monthOrder = ['January', 'February', 'March', 'April', 'May', 'June', \n                         'July', 'August', 'September', 'October', 'November', 'December'];\n      return monthOrder.indexOf(b.month) - monthOrder.indexOf(a.month);\n    })\n  })).sort((a, b) => b.year - a.year);\n\n  const formatCurrency = (amount: number) => {\n    return `£${amount.toLocaleString()}`;\n  };\n\n  const getVarianceColor = (variance: number) => {\n    if (variance > 0) return 'text-green-600';\n    if (variance < 0) return 'text-red-600';\n    return 'text-gray-600';\n  };\n\n  const getVarianceText = (variance: number) => {\n    if (variance === 0) return '£0';\n    const sign = variance > 0 ? '+' : '';\n    return `${sign}£${variance}`;\n  };\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      <Header\n        title={`Finances Total - ${formatCurrency(totalFinances)}`}\n      />\n\n      <div className=\"px-4 py-4 space-y-6\">\n        {yearTotals.map(({ year, total, finances }) => (\n          <div key={year} className=\"bg-white rounded-lg shadow-sm overflow-hidden\">\n            {/* Year Header */}\n            <div className=\"bg-gray-50 px-4 py-3 border-b\">\n              <h2 className=\"text-lg font-semibold text-gray-900\">\n                {year}/{(year + 1).toString().slice(-2)} - {formatCurrency(total)}\n              </h2>\n            </div>\n\n            {/* Table Header */}\n            <div className=\"grid grid-cols-4 gap-4 px-4 py-3 bg-gray-50 border-b text-sm font-medium text-gray-600\">\n              <div>MONTH</div>\n              <div className=\"text-right\">EXPECTED</div>\n              <div className=\"text-right\">ACTUAL</div>\n              <div className=\"text-right\">VARIANCE</div>\n            </div>\n\n            {/* Finance Rows */}\n            <div className=\"divide-y divide-gray-100\">\n              {finances.map((finance) => (\n                <div key={`${finance.year}-${finance.month}`} className=\"grid grid-cols-4 gap-4 px-4 py-3 text-sm\">\n                  <div className=\"font-medium text-gray-900\">{finance.month}</div>\n                  <div className=\"text-right text-gray-600\">\n                    {formatCurrency(finance.expected)}\n                  </div>\n                  <div className=\"text-right text-gray-900 font-medium\">\n                    {formatCurrency(finance.actual)}\n                  </div>\n                  <div className={`text-right font-medium ${getVarianceColor(finance.variance)}`}>\n                    {getVarianceText(finance.variance)}\n                  </div>\n                </div>\n              ))}\n            </div>\n          </div>\n        ))}\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAKe,SAAS;IACtB,MAAM,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,6HAAA,CAAA,SAAM,AAAD;IAEvB,2BAA2B;IAC3B,MAAM,gBAAgB,MAAM,QAAQ,CAAC,MAAM,CAAC,CAAC,OAAO,UAAY,QAAQ,QAAQ,MAAM,EAAE;IAExF,yBAAyB;IACzB,MAAM,iBAAiB,MAAM,QAAQ,CAAC,MAAM,CAAC,CAAC,KAAK;QACjD,MAAM,OAAO,QAAQ,IAAI;QACzB,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE;YACd,GAAG,CAAC,KAAK,GAAG,EAAE;QAChB;QACA,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC;QACf,OAAO;IACT,GAAG,CAAC;IAEJ,wBAAwB;IACxB,MAAM,aAAa,OAAO,OAAO,CAAC,gBAAgB,GAAG,CAAC,CAAC,CAAC,MAAM,SAAS,GAAK,CAAC;YAC3E,MAAM,SAAS;YACf,OAAO,SAAS,MAAM,CAAC,CAAC,KAAK,IAAM,MAAM,EAAE,MAAM,EAAE;YACnD,UAAU,SAAS,IAAI,CAAC,CAAC,GAAG;gBAC1B,MAAM,aAAa;oBAAC;oBAAW;oBAAY;oBAAS;oBAAS;oBAAO;oBACjD;oBAAQ;oBAAU;oBAAa;oBAAW;oBAAY;iBAAW;gBACpF,OAAO,WAAW,OAAO,CAAC,EAAE,KAAK,IAAI,WAAW,OAAO,CAAC,EAAE,KAAK;YACjE;QACF,CAAC,GAAG,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,IAAI,GAAG,EAAE,IAAI;IAElC,MAAM,iBAAiB,CAAC;QACtB,OAAO,CAAC,CAAC,EAAE,OAAO,cAAc,IAAI;IACtC;IAEA,MAAM,mBAAmB,CAAC;QACxB,IAAI,WAAW,GAAG,OAAO;QACzB,IAAI,WAAW,GAAG,OAAO;QACzB,OAAO;IACT;IAEA,MAAM,kBAAkB,CAAC;QACvB,IAAI,aAAa,GAAG,OAAO;QAC3B,MAAM,OAAO,WAAW,IAAI,MAAM;QAClC,OAAO,GAAG,KAAK,CAAC,EAAE,UAAU;IAC9B;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,sIAAA,CAAA,UAAM;gBACL,OAAO,CAAC,iBAAiB,EAAE,eAAe,gBAAgB;;;;;;0BAG5D,8OAAC;gBAAI,WAAU;0BACZ,WAAW,GAAG,CAAC,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,QAAQ,EAAE,iBACxC,8OAAC;wBAAe,WAAU;;0CAExB,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAG,WAAU;;wCACX;wCAAK;wCAAE,CAAC,OAAO,CAAC,EAAE,QAAQ,GAAG,KAAK,CAAC,CAAC;wCAAG;wCAAI,eAAe;;;;;;;;;;;;0CAK/D,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;kDAAI;;;;;;kDACL,8OAAC;wCAAI,WAAU;kDAAa;;;;;;kDAC5B,8OAAC;wCAAI,WAAU;kDAAa;;;;;;kDAC5B,8OAAC;wCAAI,WAAU;kDAAa;;;;;;;;;;;;0CAI9B,8OAAC;gCAAI,WAAU;0CACZ,SAAS,GAAG,CAAC,CAAC,wBACb,8OAAC;wCAA6C,WAAU;;0DACtD,8OAAC;gDAAI,WAAU;0DAA6B,QAAQ,KAAK;;;;;;0DACzD,8OAAC;gDAAI,WAAU;0DACZ,eAAe,QAAQ,QAAQ;;;;;;0DAElC,8OAAC;gDAAI,WAAU;0DACZ,eAAe,QAAQ,MAAM;;;;;;0DAEhC,8OAAC;gDAAI,WAAW,CAAC,uBAAuB,EAAE,iBAAiB,QAAQ,QAAQ,GAAG;0DAC3E,gBAAgB,QAAQ,QAAQ;;;;;;;uCAT3B,GAAG,QAAQ,IAAI,CAAC,CAAC,EAAE,QAAQ,KAAK,EAAE;;;;;;;;;;;uBAnBxC;;;;;;;;;;;;;;;;AAsCpB", "debugId": null}}, {"offset": {"line": 324, "column": 0}, "map": {"version": 3, "file": "search.js", "sources": ["file:///Users/<USER>/Downloads/RMR%20App%20Version%202/raising-my-rescue/node_modules/lucide-react/src/icons/search.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'm21 21-4.34-4.34', key: '14j7rj' }],\n  ['circle', { cx: '11', cy: '11', r: '8', key: '4ej97u' }],\n];\n\n/**\n * @component @name Search\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJtMjEgMjEtNC4zNC00LjM0IiAvPgogIDxjaXJjbGUgY3g9IjExIiBjeT0iMTEiIHI9IjgiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/search\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Search = createLucideIcon('search', __iconNode);\n\nexport default Search;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAoB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IACjD;QAAC,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA;YAAE,CAAI,CAAA,CAAA,CAAA,IAAA,CAAM;YAAA,CAAA,CAAI,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAM,CAAG,CAAA,CAAA,CAAA,CAAA,CAAA;YAAK,CAAK,CAAA,CAAA,CAAA,CAAA,QAAA;QAAU,CAAA;KAAA;CAC1D;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,MAAA,CAAS,CAAA,wKAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAU,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 372, "column": 0}, "map": {"version": 3, "file": "ellipsis.js", "sources": ["file:///Users/<USER>/Downloads/RMR%20App%20Version%202/raising-my-rescue/node_modules/lucide-react/src/icons/ellipsis.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['circle', { cx: '12', cy: '12', r: '1', key: '41hilf' }],\n  ['circle', { cx: '19', cy: '12', r: '1', key: '1wjl8i' }],\n  ['circle', { cx: '5', cy: '12', r: '1', key: '1pcz8c' }],\n];\n\n/**\n * @component @name Ellipsis\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8Y2lyY2xlIGN4PSIxMiIgY3k9IjEyIiByPSIxIiAvPgogIDxjaXJjbGUgY3g9IjE5IiBjeT0iMTIiIHI9IjEiIC8+CiAgPGNpcmNsZSBjeD0iNSIgY3k9IjEyIiByPSIxIiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/ellipsis\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Ellipsis = createLucideIcon('ellipsis', __iconNode);\n\nexport default Ellipsis;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,QAAU,CAAA;QAAA,CAAA;YAAE,EAAI,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAM,CAAI,CAAA,CAAA,CAAA,IAAA,CAAM;YAAA,CAAG,EAAA,CAAA,CAAA,CAAA,CAAA;YAAK,GAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAU;KAAA;IACxD;QAAC,QAAU,CAAA;QAAA,CAAA;YAAE,EAAI,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAM,CAAI,CAAA,CAAA,CAAA,IAAA,CAAM;YAAA,CAAG,EAAA,CAAA,CAAA,CAAA,CAAA;YAAK,GAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAU;KAAA;IACxD;QAAC,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA;YAAE,CAAI,CAAA,CAAA,CAAA,GAAA,CAAK;YAAA,CAAA,CAAI,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAM,CAAG,CAAA,CAAA,CAAA,CAAA,CAAA;YAAK,CAAK,CAAA,CAAA,CAAA,CAAA,QAAA;QAAU,CAAA;KAAA;CACzD;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,QAAA,CAAW,CAAA,wKAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAiB,AAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAY,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}]}