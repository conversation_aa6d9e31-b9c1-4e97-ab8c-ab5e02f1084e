{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/RMR%20App%20Version%202/raising-my-rescue/src/components/layout/Header.tsx"], "sourcesContent": ["'use client';\n\nimport { Search, Plus, Calendar, UserPlus } from 'lucide-react';\nimport { useState } from 'react';\n\ninterface HeaderButton {\n  icon: React.ComponentType<any>;\n  onClick: () => void;\n  title: string;\n}\n\ninterface HeaderProps {\n  title: string;\n  showAddButton?: boolean;\n  onAddClick?: () => void;\n  addButtonText?: string;\n  buttons?: HeaderButton[];\n  showSearch?: boolean;\n  onSearch?: (query: string) => void;\n  searchPlaceholder?: string;\n}\n\nexport default function Header({\n  title,\n  showAddButton = false,\n  onAddClick,\n  addButtonText = 'Add',\n  buttons = [],\n  showSearch = false,\n  onSearch,\n  searchPlaceholder = 'Search',\n}: HeaderProps) {\n  const [searchQuery, setSearchQuery] = useState('');\n\n  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {\n    const query = e.target.value;\n    setSearchQuery(query);\n    onSearch?.(query);\n  };\n\n  return (\n    <div className=\"bg-amber-800 text-white px-4 pb-3 safe-area-pt\">\n      {/* Top row with title and buttons */}\n      <div className=\"flex items-center justify-between mb-3\">\n        <h1 className=\"text-3xl font-semibold\">{title}</h1>\n        <div className=\"flex items-center gap-2\">\n          {/* Legacy single button support */}\n          {showAddButton && !buttons.length && (\n            <button\n              onClick={onAddClick}\n              className=\"bg-white/20 hover:bg-white/30 p-2 rounded-full transition-colors\"\n              title={addButtonText}\n            >\n              <Plus size={20} />\n            </button>\n          )}\n\n          {/* New multiple buttons support */}\n          {buttons.map((button, index) => {\n            const IconComponent = button.icon;\n            return (\n              <button\n                key={index}\n                onClick={button.onClick}\n                className=\"bg-white/20 hover:bg-white/30 p-2 rounded-full transition-colors\"\n                title={button.title}\n              >\n                <IconComponent size={20} />\n              </button>\n            );\n          })}\n        </div>\n      </div>\n\n      {/* Search bar */}\n      {showSearch && (\n        <div className=\"relative\">\n          <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-white/60\" size={20} />\n          <input\n            type=\"text\"\n            value={searchQuery}\n            onChange={handleSearchChange}\n            placeholder={searchPlaceholder}\n            className=\"w-full bg-white/20 placeholder-white/60 text-white px-10 py-3 rounded-lg focus:outline-none focus:bg-white/30 transition-colors\"\n          />\n        </div>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AACA;AAHA;;;;AAsBe,SAAS,OAAO,EAC7B,KAAK,EACL,gBAAgB,KAAK,EACrB,UAAU,EACV,gBAAgB,KAAK,EACrB,UAAU,EAAE,EACZ,aAAa,KAAK,EAClB,QAAQ,EACR,oBAAoB,QAAQ,EAChB;IACZ,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,MAAM,qBAAqB,CAAC;QAC1B,MAAM,QAAQ,EAAE,MAAM,CAAC,KAAK;QAC5B,eAAe;QACf,WAAW;IACb;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAA0B;;;;;;kCACxC,8OAAC;wBAAI,WAAU;;4BAEZ,iBAAiB,CAAC,QAAQ,MAAM,kBAC/B,8OAAC;gCACC,SAAS;gCACT,WAAU;gCACV,OAAO;0CAEP,cAAA,8OAAC,kMAAA,CAAA,OAAI;oCAAC,MAAM;;;;;;;;;;;4BAKf,QAAQ,GAAG,CAAC,CAAC,QAAQ;gCACpB,MAAM,gBAAgB,OAAO,IAAI;gCACjC,qBACE,8OAAC;oCAEC,SAAS,OAAO,OAAO;oCACvB,WAAU;oCACV,OAAO,OAAO,KAAK;8CAEnB,cAAA,8OAAC;wCAAc,MAAM;;;;;;mCALhB;;;;;4BAQX;;;;;;;;;;;;;YAKH,4BACC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,sMAAA,CAAA,SAAM;wBAAC,WAAU;wBAAmE,MAAM;;;;;;kCAC3F,8OAAC;wBACC,MAAK;wBACL,OAAO;wBACP,UAAU;wBACV,aAAa;wBACb,WAAU;;;;;;;;;;;;;;;;;;AAMtB", "debugId": null}}, {"offset": {"line": 130, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/RMR%20App%20Version%202/raising-my-rescue/src/components/modals/SlideUpModal.tsx"], "sourcesContent": ["'use client';\n\nimport { ReactNode, useEffect, useState } from 'react';\nimport { X } from 'lucide-react';\nimport { useModal } from '@/context/ModalContext';\n\ninterface SlideUpModalProps {\n  isOpen: boolean;\n  onClose: () => void;\n  title: string;\n  children: ReactNode;\n}\n\nexport default function SlideUpModal({ isOpen, onClose, title, children }: SlideUpModalProps) {\n  const { registerModal, unregisterModal } = useModal();\n  const [isVisible, setIsVisible] = useState(false);\n  const [isAnimating, setIsAnimating] = useState(false);\n  const [modalId] = useState(() => `slide-up-modal-${Math.random().toString(36).substr(2, 9)}`);\n\n  useEffect(() => {\n    if (isOpen) {\n      setIsVisible(true);\n      document.body.style.overflow = 'hidden';\n      registerModal(modalId);\n      // Small delay to trigger animation\n      setTimeout(() => setIsAnimating(true), 10);\n    } else {\n      setIsAnimating(false);\n      // Wait for animation to complete before hiding and unregistering\n      setTimeout(() => {\n        setIsVisible(false);\n        document.body.style.overflow = 'unset';\n        unregisterModal(modalId);\n      }, 300);\n    }\n\n    return () => {\n      document.body.style.overflow = 'unset';\n      unregisterModal(modalId);\n    };\n  }, [isOpen, registerModal, unregisterModal, modalId]);\n\n  const handleClose = () => {\n    setIsAnimating(false);\n    setTimeout(() => {\n      setIsVisible(false);\n      unregisterModal(modalId);\n      onClose();\n    }, 300);\n  };\n\n  const handleBackdropClick = (e: React.MouseEvent) => {\n    if (e.target === e.currentTarget) {\n      handleClose();\n    }\n  };\n\n  if (!isVisible) return null;\n\n  return (\n    <div\n      className={`fixed inset-0 z-50 transition-all duration-300 ${\n        isAnimating ? 'bg-black/50' : 'bg-black/0'\n      }`}\n      onClick={handleBackdropClick}\n    >\n      {/* Mobile: slide up from bottom */}\n      <div\n        className={`fixed bottom-0 left-0 right-0 bg-white text-black rounded-t-3xl shadow-2xl transition-transform duration-300 ease-out max-h-[85vh] overflow-hidden md:hidden ${\n          isAnimating ? 'translate-y-0' : 'translate-y-full'\n        }`}\n      >\n        {/* Handle bar */}\n        <div className=\"flex justify-center py-3\">\n          <div className=\"w-12 h-1 bg-gray-300 rounded-full\" />\n        </div>\n\n        {/* Header */}\n        <div className=\"flex items-center justify-between px-6 pb-4\">\n          <h2 className=\"text-lg font-semibold\">{title}</h2>\n          <button\n            onClick={handleClose}\n            className=\"p-2 hover:bg-gray-100 rounded-full transition-colors\"\n          >\n            <X size={20} />\n          </button>\n        </div>\n\n        {/* Content */}\n        <div className=\"px-6 pb-6 overflow-y-auto max-h-[calc(85vh-120px)]\">\n          {children}\n        </div>\n      </div>\n\n      {/* Desktop: slide in from right */}\n      <div\n        className={`fixed top-0 right-0 bottom-0 w-96 bg-white text-black shadow-2xl transition-transform duration-300 ease-out overflow-hidden hidden md:block ${\n          isAnimating ? 'translate-x-0' : 'translate-x-full'\n        }`}\n      >\n        {/* Header */}\n        <div className=\"flex items-center justify-between px-6 py-4 border-b border-gray-200\">\n          <h2 className=\"text-lg font-semibold\">{title}</h2>\n          <button\n            onClick={handleClose}\n            className=\"p-2 hover:bg-gray-100 rounded-full transition-colors\"\n          >\n            <X size={20} />\n          </button>\n        </div>\n\n        {/* Content */}\n        <div className=\"px-6 py-6 overflow-y-auto h-full\">\n          {children}\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAae,SAAS,aAAa,EAAE,MAAM,EAAE,OAAO,EAAE,KAAK,EAAE,QAAQ,EAAqB;IAC1F,MAAM,EAAE,aAAa,EAAE,eAAe,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,WAAQ,AAAD;IAClD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,IAAM,CAAC,eAAe,EAAE,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG,IAAI;IAE5F,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,QAAQ;YACV,aAAa;YACb,SAAS,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG;YAC/B,cAAc;YACd,mCAAmC;YACnC,WAAW,IAAM,eAAe,OAAO;QACzC,OAAO;YACL,eAAe;YACf,iEAAiE;YACjE,WAAW;gBACT,aAAa;gBACb,SAAS,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG;gBAC/B,gBAAgB;YAClB,GAAG;QACL;QAEA,OAAO;YACL,SAAS,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG;YAC/B,gBAAgB;QAClB;IACF,GAAG;QAAC;QAAQ;QAAe;QAAiB;KAAQ;IAEpD,MAAM,cAAc;QAClB,eAAe;QACf,WAAW;YACT,aAAa;YACb,gBAAgB;YAChB;QACF,GAAG;IACL;IAEA,MAAM,sBAAsB,CAAC;QAC3B,IAAI,EAAE,MAAM,KAAK,EAAE,aAAa,EAAE;YAChC;QACF;IACF;IAEA,IAAI,CAAC,WAAW,OAAO;IAEvB,qBACE,8OAAC;QACC,WAAW,CAAC,+CAA+C,EACzD,cAAc,gBAAgB,cAC9B;QACF,SAAS;;0BAGT,8OAAC;gBACC,WAAW,CAAC,6JAA6J,EACvK,cAAc,kBAAkB,oBAChC;;kCAGF,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;;;;;;;;;;kCAIjB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAyB;;;;;;0CACvC,8OAAC;gCACC,SAAS;gCACT,WAAU;0CAEV,cAAA,8OAAC,4LAAA,CAAA,IAAC;oCAAC,MAAM;;;;;;;;;;;;;;;;;kCAKb,8OAAC;wBAAI,WAAU;kCACZ;;;;;;;;;;;;0BAKL,8OAAC;gBACC,WAAW,CAAC,4IAA4I,EACtJ,cAAc,kBAAkB,oBAChC;;kCAGF,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAyB;;;;;;0CACvC,8OAAC;gCACC,SAAS;gCACT,WAAU;0CAEV,cAAA,8OAAC,4LAAA,CAAA,IAAC;oCAAC,MAAM;;;;;;;;;;;;;;;;;kCAKb,8OAAC;wBAAI,WAAU;kCACZ;;;;;;;;;;;;;;;;;;AAKX", "debugId": null}}, {"offset": {"line": 316, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/RMR%20App%20Version%202/raising-my-rescue/src/components/modals/SessionModal.tsx"], "sourcesContent": ["'use client';\n\nimport { Session } from '@/types';\nimport { useApp } from '@/context/AppContext';\nimport SlideUpModal from './SlideUpModal';\nimport { format } from 'date-fns';\n\ninterface SessionModalProps {\n  session: Session | null;\n  isOpen: boolean;\n  onClose: () => void;\n  onEditSession: (session: Session) => void;\n  onEditClient: (session: Session) => void;\n}\n\nexport default function SessionModal({ session, isOpen, onClose, onEditSession, onEditClient }: SessionModalProps) {\n  const { dispatch, state } = useApp();\n\n  if (!session) return null;\n\n  // Find the client for this session\n  const client = state.clients.find(c => c.id === session.clientId);\n\n  const handleDelete = () => {\n    dispatch({ type: 'DELETE_SESSION', payload: session.id });\n    onClose();\n  };\n\n\n\n  const displayName = client\n    ? `${client.firstName} ${client.lastName}${client.dogName ? ` w/ ${client.dogName}` : ''}`\n    : 'Unknown Client';\n\n  return (\n    <SlideUpModal\n      isOpen={isOpen}\n      onClose={onClose}\n      title={displayName}\n    >\n      <div className=\"space-y-6\">\n        {/* Action Buttons */}\n        <div className=\"flex gap-3\">\n          <button\n            onClick={() => onEditSession(session)}\n            className=\"flex-1 bg-amber-800 hover:bg-amber-700 text-white py-3 px-4 rounded-lg font-medium transition-colors\"\n          >\n            Edit Session\n          </button>\n          <button\n            onClick={() => onEditClient(session)}\n            className=\"flex-1 bg-amber-800 hover:bg-amber-700 text-white py-3 px-4 rounded-lg font-medium transition-colors\"\n          >\n            Edit Client\n          </button>\n        </div>\n\n        {/* Session Details */}\n        <div className=\"space-y-4\">\n          {client && (\n            <>\n              <div className=\"flex justify-between items-center\">\n                <span className=\"text-gray-600\">Owner(s) Name</span>\n                <span className=\"font-medium text-gray-900\">{client.firstName} {client.lastName}</span>\n              </div>\n\n              {client.dogName && (\n                <div className=\"flex justify-between items-center\">\n                  <span className=\"text-gray-600\">Dog(s) Name</span>\n                  <span className=\"font-medium text-gray-900\">{client.dogName}</span>\n                </div>\n              )}\n            </>\n          )}\n\n          <div className=\"flex justify-between items-center\">\n            <span className=\"text-gray-600\">Booking</span>\n            <span className=\"font-medium text-gray-900\">\n              {format(session.bookingDate, 'dd/MM/yyyy, HH:mm')}\n            </span>\n          </div>\n\n          <div className=\"flex justify-between items-center\">\n            <span className=\"text-gray-600\">Session Type</span>\n            <span className=\"font-medium text-gray-900\">{session.sessionType}</span>\n          </div>\n\n          <div className=\"flex justify-between items-center\">\n            <span className=\"text-gray-600\">Quote</span>\n            <span className=\"font-medium text-gray-900\">£{session.quote}</span>\n          </div>\n\n          {session.notes && (\n            <div className=\"flex justify-between items-start\">\n              <span className=\"text-gray-600\">Notes</span>\n              <span className=\"font-medium text-gray-900 text-right max-w-48\">{session.notes}</span>\n            </div>\n          )}\n\n        </div>\n\n        {/* Delete Button */}\n        <button\n          onClick={handleDelete}\n          className=\"w-full bg-amber-800 hover:bg-amber-700 text-white py-3 px-4 rounded-lg font-medium transition-colors mt-6\"\n        >\n          Delete Session\n        </button>\n      </div>\n    </SlideUpModal>\n  );\n}\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AALA;;;;;AAee,SAAS,aAAa,EAAE,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,aAAa,EAAE,YAAY,EAAqB;IAC/G,MAAM,EAAE,QAAQ,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,6HAAA,CAAA,SAAM,AAAD;IAEjC,IAAI,CAAC,SAAS,OAAO;IAErB,mCAAmC;IACnC,MAAM,SAAS,MAAM,OAAO,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,QAAQ,QAAQ;IAEhE,MAAM,eAAe;QACnB,SAAS;YAAE,MAAM;YAAkB,SAAS,QAAQ,EAAE;QAAC;QACvD;IACF;IAIA,MAAM,cAAc,SAChB,GAAG,OAAO,SAAS,CAAC,CAAC,EAAE,OAAO,QAAQ,GAAG,OAAO,OAAO,GAAG,CAAC,IAAI,EAAE,OAAO,OAAO,EAAE,GAAG,IAAI,GACxF;IAEJ,qBACE,8OAAC,4IAAA,CAAA,UAAY;QACX,QAAQ;QACR,SAAS;QACT,OAAO;kBAEP,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BACC,SAAS,IAAM,cAAc;4BAC7B,WAAU;sCACX;;;;;;sCAGD,8OAAC;4BACC,SAAS,IAAM,aAAa;4BAC5B,WAAU;sCACX;;;;;;;;;;;;8BAMH,8OAAC;oBAAI,WAAU;;wBACZ,wBACC;;8CACE,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAK,WAAU;sDAAgB;;;;;;sDAChC,8OAAC;4CAAK,WAAU;;gDAA6B,OAAO,SAAS;gDAAC;gDAAE,OAAO,QAAQ;;;;;;;;;;;;;gCAGhF,OAAO,OAAO,kBACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAK,WAAU;sDAAgB;;;;;;sDAChC,8OAAC;4CAAK,WAAU;sDAA6B,OAAO,OAAO;;;;;;;;;;;;;;sCAMnE,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAK,WAAU;8CAAgB;;;;;;8CAChC,8OAAC;oCAAK,WAAU;8CACb,CAAA,GAAA,qJAAA,CAAA,SAAM,AAAD,EAAE,QAAQ,WAAW,EAAE;;;;;;;;;;;;sCAIjC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAK,WAAU;8CAAgB;;;;;;8CAChC,8OAAC;oCAAK,WAAU;8CAA6B,QAAQ,WAAW;;;;;;;;;;;;sCAGlE,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAK,WAAU;8CAAgB;;;;;;8CAChC,8OAAC;oCAAK,WAAU;;wCAA4B;wCAAE,QAAQ,KAAK;;;;;;;;;;;;;wBAG5D,QAAQ,KAAK,kBACZ,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAK,WAAU;8CAAgB;;;;;;8CAChC,8OAAC;oCAAK,WAAU;8CAAiD,QAAQ,KAAK;;;;;;;;;;;;;;;;;;8BAOpF,8OAAC;oBACC,SAAS;oBACT,WAAU;8BACX;;;;;;;;;;;;;;;;;AAMT", "debugId": null}}, {"offset": {"line": 572, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/RMR%20App%20Version%202/raising-my-rescue/src/components/modals/EditSessionModal.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { Session } from '@/types';\nimport { useApp } from '@/context/AppContext';\nimport SlideUpModal from './SlideUpModal';\nimport { format } from 'date-fns';\n\ninterface EditSessionModalProps {\n  session: Session | null;\n  isOpen: boolean;\n  onClose: () => void;\n}\n\nexport default function EditSessionModal({ session, isOpen, onClose }: EditSessionModalProps) {\n  const { state, dispatch } = useApp();\n  const [formData, setFormData] = useState({\n    clientId: '',\n    sessionType: 'In-Person',\n    date: '',\n    time: '',\n    quote: '',\n    notes: ''\n  });\n\n  useEffect(() => {\n    if (session) {\n      const sessionDate = new Date(session.bookingDate);\n      setFormData({\n        clientId: session.clientId,\n        sessionType: session.sessionType,\n        date: format(sessionDate, 'yyyy-MM-dd'),\n        time: format(sessionDate, 'HH:mm'),\n        quote: session.quote.toString(),\n        notes: session.notes || ''\n      });\n    }\n  }, [session]);\n\n  const handleSubmit = (e: React.FormEvent) => {\n    e.preventDefault();\n    if (!session) return;\n\n    const bookingDate = new Date(`${formData.date}T${formData.time}`);\n\n    const updatedSession: Session = {\n      ...session,\n      clientId: formData.clientId,\n      sessionType: formData.sessionType as Session['sessionType'],\n      bookingDate,\n      quote: parseFloat(formData.quote),\n      notes: formData.notes || undefined\n    };\n\n    dispatch({ type: 'UPDATE_SESSION', payload: updatedSession });\n    onClose();\n  };\n\n  if (!session) return null;\n\n  return (\n    <SlideUpModal\n      isOpen={isOpen}\n      onClose={onClose}\n      title=\"Edit Session\"\n    >\n      <form onSubmit={handleSubmit} className=\"space-y-6\">\n        <div>\n          <label className=\"block text-gray-700 text-sm font-medium mb-2\">\n            Client\n          </label>\n          <select\n            value={formData.clientId}\n            onChange={(e) => setFormData({ ...formData, clientId: e.target.value })}\n            className=\"w-full px-4 py-3 bg-gray-50 border border-gray-300 rounded-lg text-gray-900 focus:ring-2 focus:ring-amber-500 focus:border-transparent\"\n            required\n          >\n            <option value=\"\">Select a client</option>\n            {state.clients.map((client) => (\n              <option key={client.id} value={client.id}>\n                {client.firstName} {client.lastName}{client.dogName ? ` w/ ${client.dogName}` : ''}\n              </option>\n            ))}\n          </select>\n        </div>\n\n        <div>\n          <label className=\"block text-gray-700 text-sm font-medium mb-2\">\n            Session Type\n          </label>\n          <select\n            value={formData.sessionType}\n            onChange={(e) => setFormData({ ...formData, sessionType: e.target.value })}\n            className=\"w-full px-4 py-3 bg-gray-50 border border-gray-300 rounded-lg text-gray-900 focus:ring-2 focus:ring-amber-500 focus:border-transparent\"\n          >\n            <option value=\"In-Person\">In-Person</option>\n            <option value=\"Online\">Online</option>\n            <option value=\"Training\">Training</option>\n            <option value=\"Online Catchup\">Online Catchup</option>\n            <option value=\"Group\">Group</option>\n            <option value=\"Phone Call\">Phone Call</option>\n            <option value=\"Coaching\">Coaching</option>\n          </select>\n        </div>\n\n        <div className=\"grid grid-cols-2 gap-4\">\n          <div>\n            <label className=\"block text-gray-700 text-sm font-medium mb-2\">\n              Date\n            </label>\n            <input\n              type=\"date\"\n              value={formData.date}\n              onChange={(e) => setFormData({ ...formData, date: e.target.value })}\n              className=\"w-full px-4 py-3 bg-gray-50 border border-gray-300 rounded-lg text-gray-900 focus:ring-2 focus:ring-amber-500 focus:border-transparent\"\n              required\n            />\n          </div>\n          <div>\n            <label className=\"block text-gray-700 text-sm font-medium mb-2\">\n              Time\n            </label>\n            <input\n              type=\"time\"\n              value={formData.time}\n              onChange={(e) => setFormData({ ...formData, time: e.target.value })}\n              className=\"w-full px-4 py-3 bg-gray-50 border border-gray-300 rounded-lg text-gray-900 focus:ring-2 focus:ring-amber-500 focus:border-transparent\"\n              required\n            />\n          </div>\n        </div>\n\n        <div>\n          <label className=\"block text-gray-700 text-sm font-medium mb-2\">\n            Quote (£)\n          </label>\n          <input\n            type=\"number\"\n            value={formData.quote}\n            onChange={(e) => setFormData({ ...formData, quote: e.target.value })}\n            className=\"w-full px-4 py-3 bg-gray-50 border border-gray-300 rounded-lg text-gray-900 placeholder-gray-500 focus:ring-2 focus:ring-amber-500 focus:border-transparent\"\n            placeholder=\"Enter quote amount\"\n            min=\"0\"\n            step=\"0.01\"\n            required\n          />\n        </div>\n\n\n\n        <div>\n          <label className=\"block text-gray-700 text-sm font-medium mb-2\">\n            Notes (Optional)\n          </label>\n          <textarea\n            value={formData.notes}\n            onChange={(e) => setFormData({ ...formData, notes: e.target.value })}\n            className=\"w-full px-4 py-3 bg-gray-50 border border-gray-300 rounded-lg text-gray-900 placeholder-gray-500 focus:ring-2 focus:ring-amber-500 focus:border-transparent\"\n            placeholder=\"Add any notes about the session\"\n            rows={3}\n          />\n        </div>\n\n        <button\n          type=\"submit\"\n          className=\"w-full bg-amber-800 text-white py-3 px-6 rounded-lg font-medium hover:bg-amber-700 transition-colors\"\n        >\n          Update Session\n        </button>\n      </form>\n    </SlideUpModal>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AACA;AACA;AANA;;;;;;AAce,SAAS,iBAAiB,EAAE,OAAO,EAAE,MAAM,EAAE,OAAO,EAAyB;IAC1F,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,6HAAA,CAAA,SAAM,AAAD;IACjC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,UAAU;QACV,aAAa;QACb,MAAM;QACN,MAAM;QACN,OAAO;QACP,OAAO;IACT;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,SAAS;YACX,MAAM,cAAc,IAAI,KAAK,QAAQ,WAAW;YAChD,YAAY;gBACV,UAAU,QAAQ,QAAQ;gBAC1B,aAAa,QAAQ,WAAW;gBAChC,MAAM,CAAA,GAAA,qJAAA,CAAA,SAAM,AAAD,EAAE,aAAa;gBAC1B,MAAM,CAAA,GAAA,qJAAA,CAAA,SAAM,AAAD,EAAE,aAAa;gBAC1B,OAAO,QAAQ,KAAK,CAAC,QAAQ;gBAC7B,OAAO,QAAQ,KAAK,IAAI;YAC1B;QACF;IACF,GAAG;QAAC;KAAQ;IAEZ,MAAM,eAAe,CAAC;QACpB,EAAE,cAAc;QAChB,IAAI,CAAC,SAAS;QAEd,MAAM,cAAc,IAAI,KAAK,GAAG,SAAS,IAAI,CAAC,CAAC,EAAE,SAAS,IAAI,EAAE;QAEhE,MAAM,iBAA0B;YAC9B,GAAG,OAAO;YACV,UAAU,SAAS,QAAQ;YAC3B,aAAa,SAAS,WAAW;YACjC;YACA,OAAO,WAAW,SAAS,KAAK;YAChC,OAAO,SAAS,KAAK,IAAI;QAC3B;QAEA,SAAS;YAAE,MAAM;YAAkB,SAAS;QAAe;QAC3D;IACF;IAEA,IAAI,CAAC,SAAS,OAAO;IAErB,qBACE,8OAAC,4IAAA,CAAA,UAAY;QACX,QAAQ;QACR,SAAS;QACT,OAAM;kBAEN,cAAA,8OAAC;YAAK,UAAU;YAAc,WAAU;;8BACtC,8OAAC;;sCACC,8OAAC;4BAAM,WAAU;sCAA+C;;;;;;sCAGhE,8OAAC;4BACC,OAAO,SAAS,QAAQ;4BACxB,UAAU,CAAC,IAAM,YAAY;oCAAE,GAAG,QAAQ;oCAAE,UAAU,EAAE,MAAM,CAAC,KAAK;gCAAC;4BACrE,WAAU;4BACV,QAAQ;;8CAER,8OAAC;oCAAO,OAAM;8CAAG;;;;;;gCAChB,MAAM,OAAO,CAAC,GAAG,CAAC,CAAC,uBAClB,8OAAC;wCAAuB,OAAO,OAAO,EAAE;;4CACrC,OAAO,SAAS;4CAAC;4CAAE,OAAO,QAAQ;4CAAE,OAAO,OAAO,GAAG,CAAC,IAAI,EAAE,OAAO,OAAO,EAAE,GAAG;;uCADrE,OAAO,EAAE;;;;;;;;;;;;;;;;;8BAO5B,8OAAC;;sCACC,8OAAC;4BAAM,WAAU;sCAA+C;;;;;;sCAGhE,8OAAC;4BACC,OAAO,SAAS,WAAW;4BAC3B,UAAU,CAAC,IAAM,YAAY;oCAAE,GAAG,QAAQ;oCAAE,aAAa,EAAE,MAAM,CAAC,KAAK;gCAAC;4BACxE,WAAU;;8CAEV,8OAAC;oCAAO,OAAM;8CAAY;;;;;;8CAC1B,8OAAC;oCAAO,OAAM;8CAAS;;;;;;8CACvB,8OAAC;oCAAO,OAAM;8CAAW;;;;;;8CACzB,8OAAC;oCAAO,OAAM;8CAAiB;;;;;;8CAC/B,8OAAC;oCAAO,OAAM;8CAAQ;;;;;;8CACtB,8OAAC;oCAAO,OAAM;8CAAa;;;;;;8CAC3B,8OAAC;oCAAO,OAAM;8CAAW;;;;;;;;;;;;;;;;;;8BAI7B,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;;8CACC,8OAAC;oCAAM,WAAU;8CAA+C;;;;;;8CAGhE,8OAAC;oCACC,MAAK;oCACL,OAAO,SAAS,IAAI;oCACpB,UAAU,CAAC,IAAM,YAAY;4CAAE,GAAG,QAAQ;4CAAE,MAAM,EAAE,MAAM,CAAC,KAAK;wCAAC;oCACjE,WAAU;oCACV,QAAQ;;;;;;;;;;;;sCAGZ,8OAAC;;8CACC,8OAAC;oCAAM,WAAU;8CAA+C;;;;;;8CAGhE,8OAAC;oCACC,MAAK;oCACL,OAAO,SAAS,IAAI;oCACpB,UAAU,CAAC,IAAM,YAAY;4CAAE,GAAG,QAAQ;4CAAE,MAAM,EAAE,MAAM,CAAC,KAAK;wCAAC;oCACjE,WAAU;oCACV,QAAQ;;;;;;;;;;;;;;;;;;8BAKd,8OAAC;;sCACC,8OAAC;4BAAM,WAAU;sCAA+C;;;;;;sCAGhE,8OAAC;4BACC,MAAK;4BACL,OAAO,SAAS,KAAK;4BACrB,UAAU,CAAC,IAAM,YAAY;oCAAE,GAAG,QAAQ;oCAAE,OAAO,EAAE,MAAM,CAAC,KAAK;gCAAC;4BAClE,WAAU;4BACV,aAAY;4BACZ,KAAI;4BACJ,MAAK;4BACL,QAAQ;;;;;;;;;;;;8BAMZ,8OAAC;;sCACC,8OAAC;4BAAM,WAAU;sCAA+C;;;;;;sCAGhE,8OAAC;4BACC,OAAO,SAAS,KAAK;4BACrB,UAAU,CAAC,IAAM,YAAY;oCAAE,GAAG,QAAQ;oCAAE,OAAO,EAAE,MAAM,CAAC,KAAK;gCAAC;4BAClE,WAAU;4BACV,aAAY;4BACZ,MAAM;;;;;;;;;;;;8BAIV,8OAAC;oBACC,MAAK;oBACL,WAAU;8BACX;;;;;;;;;;;;;;;;;AAMT", "debugId": null}}, {"offset": {"line": 935, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/RMR%20App%20Version%202/raising-my-rescue/src/components/modals/EditClientModal.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { Client } from '@/types';\nimport { useApp } from '@/context/AppContext';\nimport SlideUpModal from './SlideUpModal';\n\ninterface EditClientModalProps {\n  client: Client | null;\n  isOpen: boolean;\n  onClose: () => void;\n}\n\nexport default function EditClientModal({ client, isOpen, onClose }: EditClientModalProps) {\n  const { dispatch } = useApp();\n  const [formData, setFormData] = useState({\n    firstName: '',\n    lastName: '',\n    email: '',\n    phone: '',\n    dogName: '',\n    otherDogs: [''],\n    address: '',\n    active: true,\n    membership: false\n  });\n\n  useEffect(() => {\n    if (client) {\n      setFormData({\n        firstName: client.firstName,\n        lastName: client.lastName,\n        email: client.email || '',\n        phone: client.phone || '',\n        dogName: client.dogName || '',\n        otherDogs: client.otherDogs || [''],\n        address: client.address || '',\n        active: client.active,\n        membership: client.membership\n      });\n    }\n  }, [client]);\n\n  const addDogField = () => {\n    setFormData({ ...formData, otherDogs: [...formData.otherDogs, ''] });\n  };\n\n  const removeDogField = (index: number) => {\n    const newOtherDogs = formData.otherDogs.filter((_, i) => i !== index);\n    setFormData({ ...formData, otherDogs: newOtherDogs });\n  };\n\n  const updateDogField = (index: number, value: string) => {\n    const newOtherDogs = [...formData.otherDogs];\n    newOtherDogs[index] = value;\n    setFormData({ ...formData, otherDogs: newOtherDogs });\n  };\n\n  const handleSubmit = (e: React.FormEvent) => {\n    e.preventDefault();\n    if (!client) return;\n\n    const updatedClient: Client = {\n      ...client,\n      firstName: formData.firstName,\n      lastName: formData.lastName,\n      email: formData.email || undefined,\n      phone: formData.phone || undefined,\n      dogName: formData.dogName || undefined,\n      otherDogs: formData.otherDogs.filter(dog => dog.trim() !== '') || undefined,\n      address: formData.address || undefined,\n      active: formData.active,\n      membership: formData.membership\n    };\n\n    dispatch({ type: 'UPDATE_CLIENT', payload: updatedClient });\n    onClose();\n  };\n\n  if (!client) return null;\n\n  return (\n    <SlideUpModal\n      isOpen={isOpen}\n      onClose={onClose}\n      title=\"Edit Client\"\n    >\n      <form onSubmit={handleSubmit} className=\"space-y-6\">\n        <div className=\"grid grid-cols-2 gap-4\">\n          <div>\n            <label className=\"block text-gray-700 text-sm font-medium mb-2\">\n              First Name\n            </label>\n            <input\n              type=\"text\"\n              value={formData.firstName}\n              onChange={(e) => setFormData({ ...formData, firstName: e.target.value })}\n              className=\"w-full px-4 py-3 bg-gray-50 border border-gray-300 rounded-lg text-gray-900 placeholder-gray-500 focus:ring-2 focus:ring-amber-500 focus:border-transparent\"\n              placeholder=\"Enter first name\"\n              required\n            />\n          </div>\n          <div>\n            <label className=\"block text-gray-700 text-sm font-medium mb-2\">\n              Last Name\n            </label>\n            <input\n              type=\"text\"\n              value={formData.lastName}\n              onChange={(e) => setFormData({ ...formData, lastName: e.target.value })}\n              className=\"w-full px-4 py-3 bg-gray-50 border border-gray-300 rounded-lg text-gray-900 placeholder-gray-500 focus:ring-2 focus:ring-amber-500 focus:border-transparent\"\n              placeholder=\"Enter last name\"\n              required\n            />\n          </div>\n        </div>\n\n        <div>\n          <label className=\"block text-gray-700 text-sm font-medium mb-2\">\n            Dog Name\n          </label>\n          <input\n            type=\"text\"\n            value={formData.dogName}\n            onChange={(e) => setFormData({ ...formData, dogName: e.target.value })}\n            className=\"w-full px-4 py-3 bg-gray-50 border border-gray-300 rounded-lg text-gray-900 placeholder-gray-500 focus:ring-2 focus:ring-amber-500 focus:border-transparent\"\n            placeholder=\"Enter primary dog name\"\n            required\n          />\n        </div>\n\n        <div>\n          <label className=\"block text-gray-700 text-sm font-medium mb-2\">\n            Other Dogs (Optional)\n          </label>\n          {formData.otherDogs.map((dog, index) => (\n            <div key={index} className=\"flex gap-2 mb-2\">\n              <input\n                type=\"text\"\n                value={dog}\n                onChange={(e) => updateDogField(index, e.target.value)}\n                className=\"flex-1 px-4 py-3 bg-gray-50 border border-gray-300 rounded-lg text-gray-900 placeholder-gray-500 focus:ring-2 focus:ring-amber-500 focus:border-transparent\"\n                placeholder=\"Enter dog name\"\n              />\n              {formData.otherDogs.length > 1 && (\n                <button\n                  type=\"button\"\n                  onClick={() => removeDogField(index)}\n                  className=\"px-3 py-3 bg-red-500 text-white rounded-lg hover:bg-red-600\"\n                >\n                  ×\n                </button>\n              )}\n            </div>\n          ))}\n          <button\n            type=\"button\"\n            onClick={addDogField}\n            className=\"text-amber-600 hover:text-amber-700 text-sm font-medium\"\n          >\n            + Add another dog\n          </button>\n        </div>\n\n        <div>\n          <label className=\"block text-gray-700 text-sm font-medium mb-2\">\n            Phone (Optional)\n          </label>\n          <input\n            type=\"tel\"\n            value={formData.phone}\n            onChange={(e) => setFormData({ ...formData, phone: e.target.value })}\n            className=\"w-full px-4 py-3 bg-gray-50 border border-gray-300 rounded-lg text-gray-900 placeholder-gray-500 focus:ring-2 focus:ring-amber-500 focus:border-transparent\"\n            placeholder=\"Enter phone number\"\n          />\n        </div>\n\n        <div>\n          <label className=\"block text-gray-700 text-sm font-medium mb-2\">\n            Email (Optional)\n          </label>\n          <input\n            type=\"email\"\n            value={formData.email}\n            onChange={(e) => setFormData({ ...formData, email: e.target.value })}\n            className=\"w-full px-4 py-3 bg-gray-50 border border-gray-300 rounded-lg text-gray-900 placeholder-gray-500 focus:ring-2 focus:ring-amber-500 focus:border-transparent\"\n            placeholder=\"Enter email address\"\n          />\n        </div>\n\n        <div>\n          <label className=\"block text-gray-700 text-sm font-medium mb-2\">\n            Address (Optional)\n          </label>\n          <textarea\n            value={formData.address}\n            onChange={(e) => setFormData({ ...formData, address: e.target.value })}\n            className=\"w-full px-4 py-3 bg-gray-50 border border-gray-300 rounded-lg text-gray-900 placeholder-gray-500 focus:ring-2 focus:ring-amber-500 focus:border-transparent\"\n            placeholder=\"Enter full address\"\n            rows={2}\n          />\n        </div>\n\n        <div className=\"grid grid-cols-2 gap-4\">\n          <div className=\"flex items-center justify-between\">\n            <label className=\"text-gray-700 font-medium\">\n              Active Client\n            </label>\n            <button\n              type=\"button\"\n              onClick={() => setFormData({ ...formData, active: !formData.active })}\n              className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${\n                formData.active ? 'bg-amber-600' : 'bg-gray-200'\n              }`}\n            >\n              <span\n                className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${\n                  formData.active ? 'translate-x-6' : 'translate-x-1'\n                }`}\n              />\n            </button>\n          </div>\n          <div className=\"flex items-center justify-between\">\n            <label className=\"text-gray-700 font-medium\">\n              Membership\n            </label>\n            <button\n              type=\"button\"\n              onClick={() => setFormData({ ...formData, membership: !formData.membership })}\n              className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${\n                formData.membership ? 'bg-amber-600' : 'bg-gray-200'\n              }`}\n            >\n              <span\n                className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${\n                  formData.membership ? 'translate-x-6' : 'translate-x-1'\n                }`}\n              />\n            </button>\n          </div>\n        </div>\n\n        <button\n          type=\"submit\"\n          className=\"w-full bg-amber-800 text-white py-3 px-6 rounded-lg font-medium hover:bg-amber-700 transition-colors\"\n        >\n          Update Client\n        </button>\n      </form>\n    </SlideUpModal>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AACA;AALA;;;;;AAae,SAAS,gBAAgB,EAAE,MAAM,EAAE,MAAM,EAAE,OAAO,EAAwB;IACvF,MAAM,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,6HAAA,CAAA,SAAM,AAAD;IAC1B,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,WAAW;QACX,UAAU;QACV,OAAO;QACP,OAAO;QACP,SAAS;QACT,WAAW;YAAC;SAAG;QACf,SAAS;QACT,QAAQ;QACR,YAAY;IACd;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,QAAQ;YACV,YAAY;gBACV,WAAW,OAAO,SAAS;gBAC3B,UAAU,OAAO,QAAQ;gBACzB,OAAO,OAAO,KAAK,IAAI;gBACvB,OAAO,OAAO,KAAK,IAAI;gBACvB,SAAS,OAAO,OAAO,IAAI;gBAC3B,WAAW,OAAO,SAAS,IAAI;oBAAC;iBAAG;gBACnC,SAAS,OAAO,OAAO,IAAI;gBAC3B,QAAQ,OAAO,MAAM;gBACrB,YAAY,OAAO,UAAU;YAC/B;QACF;IACF,GAAG;QAAC;KAAO;IAEX,MAAM,cAAc;QAClB,YAAY;YAAE,GAAG,QAAQ;YAAE,WAAW;mBAAI,SAAS,SAAS;gBAAE;aAAG;QAAC;IACpE;IAEA,MAAM,iBAAiB,CAAC;QACtB,MAAM,eAAe,SAAS,SAAS,CAAC,MAAM,CAAC,CAAC,GAAG,IAAM,MAAM;QAC/D,YAAY;YAAE,GAAG,QAAQ;YAAE,WAAW;QAAa;IACrD;IAEA,MAAM,iBAAiB,CAAC,OAAe;QACrC,MAAM,eAAe;eAAI,SAAS,SAAS;SAAC;QAC5C,YAAY,CAAC,MAAM,GAAG;QACtB,YAAY;YAAE,GAAG,QAAQ;YAAE,WAAW;QAAa;IACrD;IAEA,MAAM,eAAe,CAAC;QACpB,EAAE,cAAc;QAChB,IAAI,CAAC,QAAQ;QAEb,MAAM,gBAAwB;YAC5B,GAAG,MAAM;YACT,WAAW,SAAS,SAAS;YAC7B,UAAU,SAAS,QAAQ;YAC3B,OAAO,SAAS,KAAK,IAAI;YACzB,OAAO,SAAS,KAAK,IAAI;YACzB,SAAS,SAAS,OAAO,IAAI;YAC7B,WAAW,SAAS,SAAS,CAAC,MAAM,CAAC,CAAA,MAAO,IAAI,IAAI,OAAO,OAAO;YAClE,SAAS,SAAS,OAAO,IAAI;YAC7B,QAAQ,SAAS,MAAM;YACvB,YAAY,SAAS,UAAU;QACjC;QAEA,SAAS;YAAE,MAAM;YAAiB,SAAS;QAAc;QACzD;IACF;IAEA,IAAI,CAAC,QAAQ,OAAO;IAEpB,qBACE,8OAAC,4IAAA,CAAA,UAAY;QACX,QAAQ;QACR,SAAS;QACT,OAAM;kBAEN,cAAA,8OAAC;YAAK,UAAU;YAAc,WAAU;;8BACtC,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;;8CACC,8OAAC;oCAAM,WAAU;8CAA+C;;;;;;8CAGhE,8OAAC;oCACC,MAAK;oCACL,OAAO,SAAS,SAAS;oCACzB,UAAU,CAAC,IAAM,YAAY;4CAAE,GAAG,QAAQ;4CAAE,WAAW,EAAE,MAAM,CAAC,KAAK;wCAAC;oCACtE,WAAU;oCACV,aAAY;oCACZ,QAAQ;;;;;;;;;;;;sCAGZ,8OAAC;;8CACC,8OAAC;oCAAM,WAAU;8CAA+C;;;;;;8CAGhE,8OAAC;oCACC,MAAK;oCACL,OAAO,SAAS,QAAQ;oCACxB,UAAU,CAAC,IAAM,YAAY;4CAAE,GAAG,QAAQ;4CAAE,UAAU,EAAE,MAAM,CAAC,KAAK;wCAAC;oCACrE,WAAU;oCACV,aAAY;oCACZ,QAAQ;;;;;;;;;;;;;;;;;;8BAKd,8OAAC;;sCACC,8OAAC;4BAAM,WAAU;sCAA+C;;;;;;sCAGhE,8OAAC;4BACC,MAAK;4BACL,OAAO,SAAS,OAAO;4BACvB,UAAU,CAAC,IAAM,YAAY;oCAAE,GAAG,QAAQ;oCAAE,SAAS,EAAE,MAAM,CAAC,KAAK;gCAAC;4BACpE,WAAU;4BACV,aAAY;4BACZ,QAAQ;;;;;;;;;;;;8BAIZ,8OAAC;;sCACC,8OAAC;4BAAM,WAAU;sCAA+C;;;;;;wBAG/D,SAAS,SAAS,CAAC,GAAG,CAAC,CAAC,KAAK,sBAC5B,8OAAC;gCAAgB,WAAU;;kDACzB,8OAAC;wCACC,MAAK;wCACL,OAAO;wCACP,UAAU,CAAC,IAAM,eAAe,OAAO,EAAE,MAAM,CAAC,KAAK;wCACrD,WAAU;wCACV,aAAY;;;;;;oCAEb,SAAS,SAAS,CAAC,MAAM,GAAG,mBAC3B,8OAAC;wCACC,MAAK;wCACL,SAAS,IAAM,eAAe;wCAC9B,WAAU;kDACX;;;;;;;+BAbK;;;;;sCAmBZ,8OAAC;4BACC,MAAK;4BACL,SAAS;4BACT,WAAU;sCACX;;;;;;;;;;;;8BAKH,8OAAC;;sCACC,8OAAC;4BAAM,WAAU;sCAA+C;;;;;;sCAGhE,8OAAC;4BACC,MAAK;4BACL,OAAO,SAAS,KAAK;4BACrB,UAAU,CAAC,IAAM,YAAY;oCAAE,GAAG,QAAQ;oCAAE,OAAO,EAAE,MAAM,CAAC,KAAK;gCAAC;4BAClE,WAAU;4BACV,aAAY;;;;;;;;;;;;8BAIhB,8OAAC;;sCACC,8OAAC;4BAAM,WAAU;sCAA+C;;;;;;sCAGhE,8OAAC;4BACC,MAAK;4BACL,OAAO,SAAS,KAAK;4BACrB,UAAU,CAAC,IAAM,YAAY;oCAAE,GAAG,QAAQ;oCAAE,OAAO,EAAE,MAAM,CAAC,KAAK;gCAAC;4BAClE,WAAU;4BACV,aAAY;;;;;;;;;;;;8BAIhB,8OAAC;;sCACC,8OAAC;4BAAM,WAAU;sCAA+C;;;;;;sCAGhE,8OAAC;4BACC,OAAO,SAAS,OAAO;4BACvB,UAAU,CAAC,IAAM,YAAY;oCAAE,GAAG,QAAQ;oCAAE,SAAS,EAAE,MAAM,CAAC,KAAK;gCAAC;4BACpE,WAAU;4BACV,aAAY;4BACZ,MAAM;;;;;;;;;;;;8BAIV,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAM,WAAU;8CAA4B;;;;;;8CAG7C,8OAAC;oCACC,MAAK;oCACL,SAAS,IAAM,YAAY;4CAAE,GAAG,QAAQ;4CAAE,QAAQ,CAAC,SAAS,MAAM;wCAAC;oCACnE,WAAW,CAAC,0EAA0E,EACpF,SAAS,MAAM,GAAG,iBAAiB,eACnC;8CAEF,cAAA,8OAAC;wCACC,WAAW,CAAC,0EAA0E,EACpF,SAAS,MAAM,GAAG,kBAAkB,iBACpC;;;;;;;;;;;;;;;;;sCAIR,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAM,WAAU;8CAA4B;;;;;;8CAG7C,8OAAC;oCACC,MAAK;oCACL,SAAS,IAAM,YAAY;4CAAE,GAAG,QAAQ;4CAAE,YAAY,CAAC,SAAS,UAAU;wCAAC;oCAC3E,WAAW,CAAC,0EAA0E,EACpF,SAAS,UAAU,GAAG,iBAAiB,eACvC;8CAEF,cAAA,8OAAC;wCACC,WAAW,CAAC,0EAA0E,EACpF,SAAS,UAAU,GAAG,kBAAkB,iBACxC;;;;;;;;;;;;;;;;;;;;;;;8BAMV,8OAAC;oBACC,MAAK;oBACL,WAAU;8BACX;;;;;;;;;;;;;;;;;AAMT", "debugId": null}}, {"offset": {"line": 1393, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/RMR%20App%20Version%202/raising-my-rescue/src/utils/pricing.ts"], "sourcesContent": ["import { Session } from '@/types';\n\nexport const calculateQuote = (sessionType: Session['sessionType'], isMember: boolean): number => {\n  switch (sessionType) {\n    case 'Online':\n    case 'Training':\n      return isMember ? 50 : 60;\n    case 'In-Person':\n      return isMember ? 95 : 75;\n    case 'Online Catchup':\n      return 30;\n    case 'Group':\n    case 'Phone Call':\n    case 'Coaching':\n      // These can be manually set, return 0 as default\n      return 0;\n    default:\n      return 0;\n  }\n};\n"], "names": [], "mappings": ";;;AAEO,MAAM,iBAAiB,CAAC,aAAqC;IAClE,OAAQ;QACN,KAAK;QACL,KAAK;YACH,OAAO,WAAW,KAAK;QACzB,KAAK;YACH,OAAO,WAAW,KAAK;QACzB,KAAK;YACH,OAAO;QACT,KAAK;QACL,KAAK;QACL,KAAK;YACH,iDAAiD;YACjD,OAAO;QACT;YACE,OAAO;IACX;AACF", "debugId": null}}, {"offset": {"line": 1420, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/RMR%20App%20Version%202/raising-my-rescue/src/components/AddModal.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { X } from 'lucide-react';\nimport { useModal } from '@/context/ModalContext';\nimport { useApp } from '@/context/AppContext';\nimport { Session, Client } from '@/types';\nimport { calculateQuote } from '@/utils/pricing';\n\ninterface AddModalProps {\n  isOpen: boolean;\n  onClose: () => void;\n  type: 'session' | 'client';\n}\n\nexport default function AddModal({ isOpen, onClose, type }: AddModalProps) {\n  const [isVisible, setIsVisible] = useState(false);\n  const [isAnimating, setIsAnimating] = useState(false);\n  const { registerModal, unregisterModal } = useModal();\n  const [modalId] = useState(() => `add-modal-${Math.random().toString(36).substr(2, 9)}`);\n\n  useEffect(() => {\n    if (isOpen) {\n      setIsVisible(true);\n      registerModal(modalId);\n      // Small delay to trigger animation\n      setTimeout(() => setIsAnimating(true), 10);\n    } else {\n      setIsAnimating(false);\n      // Wait for animation to complete before hiding and unregistering\n      setTimeout(() => {\n        setIsVisible(false);\n        unregisterModal(modalId);\n      }, 300);\n    }\n  }, [isOpen, registerModal, unregisterModal, modalId]);\n\n  const handleClose = () => {\n    setIsAnimating(false);\n    setTimeout(() => {\n      setIsVisible(false);\n      unregisterModal(modalId);\n      onClose();\n    }, 300);\n  };\n\n  const handleBackdropClick = (e: React.MouseEvent) => {\n    if (e.target === e.currentTarget) {\n      handleClose();\n    }\n  };\n\n  if (!isVisible) return null;\n\n  return (\n    <div\n      className={`fixed inset-0 z-50 transition-all duration-300 ${\n        isAnimating ? 'bg-black/50' : 'bg-black/0'\n      }`}\n      onClick={handleBackdropClick}\n    >\n      {/* Mobile: slide up from bottom */}\n      <div\n        className={`fixed bottom-0 left-0 right-0 bg-white rounded-t-3xl shadow-2xl transition-transform duration-300 ease-out md:hidden ${\n          isAnimating ? 'translate-y-0' : 'translate-y-full'\n        }`}\n        style={{ maxHeight: '90vh' }}\n      >\n        {/* Header */}\n        <div className=\"flex items-center justify-between p-6 border-b border-gray-200\">\n          <h2 className=\"text-xl font-semibold\">\n            {type === 'session' ? 'Add Session' : 'Add Client'}\n          </h2>\n          <button\n            onClick={handleClose}\n            className=\"p-2 hover:bg-gray-100 rounded-full transition-colors\"\n          >\n            <X size={24} />\n          </button>\n        </div>\n\n        {/* Content */}\n        <div className=\"p-6 overflow-y-auto\" style={{ maxHeight: 'calc(90vh - 80px)' }}>\n          {type === 'session' ? (\n            <SessionForm onSubmit={handleClose} />\n          ) : (\n            <ClientForm onSubmit={handleClose} />\n          )}\n        </div>\n      </div>\n\n      {/* Desktop: slide in from right */}\n      <div\n        className={`fixed top-0 right-0 bottom-0 w-96 bg-white shadow-2xl transition-transform duration-300 ease-out overflow-hidden hidden md:block ${\n          isAnimating ? 'translate-x-0' : 'translate-x-full'\n        }`}\n      >\n        {/* Header */}\n        <div className=\"flex items-center justify-between p-6 border-b border-gray-200\">\n          <h2 className=\"text-xl font-semibold\">\n            {type === 'session' ? 'Add Session' : 'Add Client'}\n          </h2>\n          <button\n            onClick={handleClose}\n            className=\"p-2 hover:bg-gray-100 rounded-full transition-colors\"\n          >\n            <X size={24} />\n          </button>\n        </div>\n\n        {/* Content */}\n        <div className=\"p-6 overflow-y-auto h-full\">\n          {type === 'session' ? (\n            <SessionForm onSubmit={handleClose} />\n          ) : (\n            <ClientForm onSubmit={handleClose} />\n          )}\n        </div>\n      </div>\n    </div>\n  );\n}\n\nfunction SessionForm({ onSubmit }: { onSubmit: () => void }) {\n  const { state } = useApp();\n  const [formData, setFormData] = useState({\n    clientId: '',\n    sessionType: 'In-Person' as Session['sessionType'],\n    date: '',\n    time: '',\n    notes: '',\n    quote: 0\n  });\n\n  const [selectedClient, setSelectedClient] = useState<Client | null>(null);\n\n  const handleClientChange = (clientId: string) => {\n    const client = state.clients.find(c => c.id === clientId);\n    setSelectedClient(client || null);\n    setFormData({\n      ...formData,\n      clientId,\n      quote: calculateQuote(formData.sessionType, client?.membership || false)\n    });\n  };\n\n  const handleSessionTypeChange = (sessionType: Session['sessionType']) => {\n    setFormData({\n      ...formData,\n      sessionType,\n      quote: calculateQuote(sessionType, selectedClient?.membership || false)\n    });\n  };\n\n  const handleSubmit = (e: React.FormEvent) => {\n    e.preventDefault();\n    // TODO: Implement session creation\n    console.log('Creating session:', formData);\n    onSubmit();\n  };\n\n  return (\n    <form onSubmit={handleSubmit} className=\"space-y-6\">\n      <div>\n        <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n          Client\n        </label>\n        <select\n          value={formData.clientId}\n          onChange={(e) => handleClientChange(e.target.value)}\n          className=\"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-transparent\"\n          required\n        >\n          <option value=\"\">Select a client</option>\n          {state.clients.map(client => (\n            <option key={client.id} value={client.id}>\n              {client.firstName} {client.lastName}{client.dogName ? ` w/ ${client.dogName}` : ''}\n            </option>\n          ))}\n        </select>\n      </div>\n\n      <div>\n        <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n          Session Type\n        </label>\n        <select\n          value={formData.sessionType}\n          onChange={(e) => handleSessionTypeChange(e.target.value as Session['sessionType'])}\n          className=\"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-transparent\"\n        >\n          <option value=\"In-Person\">In-Person</option>\n          <option value=\"Online\">Online</option>\n          <option value=\"Training\">Training</option>\n          <option value=\"Online Catchup\">Online Catchup</option>\n          <option value=\"Group\">Group</option>\n          <option value=\"Phone Call\">Phone Call</option>\n          <option value=\"Coaching\">Coaching</option>\n        </select>\n      </div>\n\n      <div className=\"grid grid-cols-2 gap-4\">\n        <div>\n          <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n            Date\n          </label>\n          <input\n            type=\"date\"\n            value={formData.date}\n            onChange={(e) => setFormData({ ...formData, date: e.target.value })}\n            className=\"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-transparent\"\n            required\n          />\n        </div>\n\n        <div>\n          <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n            Time\n          </label>\n          <input\n            type=\"time\"\n            value={formData.time}\n            onChange={(e) => setFormData({ ...formData, time: e.target.value })}\n            className=\"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-transparent\"\n            required\n          />\n        </div>\n      </div>\n\n      <div>\n        <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n          Quote (£)\n        </label>\n        <input\n          type=\"number\"\n          value={formData.quote}\n          onChange={(e) => setFormData({ ...formData, quote: parseFloat(e.target.value) || 0 })}\n          className=\"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-transparent\"\n          placeholder=\"Enter quote amount\"\n          min=\"0\"\n          step=\"0.01\"\n          required\n        />\n        {selectedClient && (\n          <p className=\"text-sm text-gray-500 mt-1\">\n            Auto-calculated: £{calculateQuote(formData.sessionType, selectedClient.membership)}\n            {selectedClient.membership ? ' (Member)' : ' (Non-member)'}\n          </p>\n        )}\n      </div>\n\n      <div>\n        <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n          Notes (Optional)\n        </label>\n        <textarea\n          value={formData.notes}\n          onChange={(e) => setFormData({ ...formData, notes: e.target.value })}\n          className=\"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-transparent\"\n          placeholder=\"Add any notes about the session\"\n          rows={3}\n        />\n      </div>\n\n      <button\n        type=\"submit\"\n        className=\"w-full bg-amber-800 text-white py-3 px-6 rounded-lg font-medium hover:bg-amber-700 transition-colors\"\n      >\n        Create Session\n      </button>\n    </form>\n  );\n}\n\nfunction ClientForm({ onSubmit }: { onSubmit: () => void }) {\n  const [formData, setFormData] = useState({\n    firstName: '',\n    lastName: '',\n    dogName: '',\n    otherDogs: [''],\n    phone: '',\n    email: '',\n    address: '',\n    active: true,\n    membership: false\n  });\n\n  const handleSubmit = (e: React.FormEvent) => {\n    e.preventDefault();\n    // TODO: Implement client creation\n    console.log('Creating client:', formData);\n    onSubmit();\n  };\n\n  const addDogField = () => {\n    setFormData({ ...formData, otherDogs: [...formData.otherDogs, ''] });\n  };\n\n  const removeDogField = (index: number) => {\n    const newOtherDogs = formData.otherDogs.filter((_, i) => i !== index);\n    setFormData({ ...formData, otherDogs: newOtherDogs });\n  };\n\n  const updateDogField = (index: number, value: string) => {\n    const newOtherDogs = [...formData.otherDogs];\n    newOtherDogs[index] = value;\n    setFormData({ ...formData, otherDogs: newOtherDogs });\n  };\n\n  return (\n    <form onSubmit={handleSubmit} className=\"space-y-6\">\n      <div className=\"grid grid-cols-2 gap-4\">\n        <div>\n          <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n            First Name\n          </label>\n          <input\n            type=\"text\"\n            value={formData.firstName}\n            onChange={(e) => setFormData({ ...formData, firstName: e.target.value })}\n            className=\"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-transparent\"\n            placeholder=\"Enter first name\"\n            required\n          />\n        </div>\n        <div>\n          <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n            Last Name\n          </label>\n          <input\n            type=\"text\"\n            value={formData.lastName}\n            onChange={(e) => setFormData({ ...formData, lastName: e.target.value })}\n            className=\"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-transparent\"\n            placeholder=\"Enter last name\"\n            required\n          />\n        </div>\n      </div>\n\n      <div>\n        <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n          Dog Name\n        </label>\n        <input\n          type=\"text\"\n          value={formData.dogName}\n          onChange={(e) => setFormData({ ...formData, dogName: e.target.value })}\n          className=\"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-transparent\"\n          placeholder=\"Enter primary dog name\"\n          required\n        />\n      </div>\n\n      <div>\n        <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n          Other Dogs (Optional)\n        </label>\n        {formData.otherDogs.map((dog, index) => (\n          <div key={index} className=\"flex gap-2 mb-2\">\n            <input\n              type=\"text\"\n              value={dog}\n              onChange={(e) => updateDogField(index, e.target.value)}\n              className=\"flex-1 px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-transparent\"\n              placeholder=\"Enter dog name\"\n            />\n            {formData.otherDogs.length > 1 && (\n              <button\n                type=\"button\"\n                onClick={() => removeDogField(index)}\n                className=\"px-3 py-3 bg-red-500 text-white rounded-lg hover:bg-red-600\"\n              >\n                ×\n              </button>\n            )}\n          </div>\n        ))}\n        <button\n          type=\"button\"\n          onClick={addDogField}\n          className=\"text-amber-600 hover:text-amber-700 text-sm font-medium\"\n        >\n          + Add another dog\n        </button>\n      </div>\n\n      <div>\n        <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n          Phone\n        </label>\n        <input\n          type=\"tel\"\n          value={formData.phone}\n          onChange={(e) => setFormData({ ...formData, phone: e.target.value })}\n          className=\"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-transparent\"\n          placeholder=\"Enter phone number\"\n        />\n      </div>\n\n      <div>\n        <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n          Email\n        </label>\n        <input\n          type=\"email\"\n          value={formData.email}\n          onChange={(e) => setFormData({ ...formData, email: e.target.value })}\n          className=\"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-transparent\"\n          placeholder=\"Enter email\"\n        />\n      </div>\n\n      <div>\n        <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n          Address\n        </label>\n        <textarea\n          value={formData.address}\n          onChange={(e) => setFormData({ ...formData, address: e.target.value })}\n          className=\"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-transparent\"\n          placeholder=\"Enter full address\"\n          rows={2}\n        />\n      </div>\n\n      <div className=\"grid grid-cols-2 gap-4\">\n        <div className=\"flex items-center justify-between\">\n          <label className=\"text-sm font-medium text-gray-700\">\n            Active Client\n          </label>\n          <button\n            type=\"button\"\n            onClick={() => setFormData({ ...formData, active: !formData.active })}\n            className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${\n              formData.active ? 'bg-amber-600' : 'bg-gray-200'\n            }`}\n          >\n            <span\n              className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${\n                formData.active ? 'translate-x-6' : 'translate-x-1'\n              }`}\n            />\n          </button>\n        </div>\n        <div className=\"flex items-center justify-between\">\n          <label className=\"text-sm font-medium text-gray-700\">\n            Membership\n          </label>\n          <button\n            type=\"button\"\n            onClick={() => setFormData({ ...formData, membership: !formData.membership })}\n            className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${\n              formData.membership ? 'bg-amber-600' : 'bg-gray-200'\n            }`}\n          >\n            <span\n              className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${\n                formData.membership ? 'translate-x-6' : 'translate-x-1'\n              }`}\n            />\n          </button>\n        </div>\n      </div>\n\n      <button\n        type=\"submit\"\n        className=\"w-full bg-amber-800 text-white py-3 px-6 rounded-lg font-medium hover:bg-amber-700 transition-colors\"\n      >\n        Create Client\n      </button>\n    </form>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAEA;AAPA;;;;;;;AAee,SAAS,SAAS,EAAE,MAAM,EAAE,OAAO,EAAE,IAAI,EAAiB;IACvE,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,EAAE,aAAa,EAAE,eAAe,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,WAAQ,AAAD;IAClD,MAAM,CAAC,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,IAAM,CAAC,UAAU,EAAE,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG,IAAI;IAEvF,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,QAAQ;YACV,aAAa;YACb,cAAc;YACd,mCAAmC;YACnC,WAAW,IAAM,eAAe,OAAO;QACzC,OAAO;YACL,eAAe;YACf,iEAAiE;YACjE,WAAW;gBACT,aAAa;gBACb,gBAAgB;YAClB,GAAG;QACL;IACF,GAAG;QAAC;QAAQ;QAAe;QAAiB;KAAQ;IAEpD,MAAM,cAAc;QAClB,eAAe;QACf,WAAW;YACT,aAAa;YACb,gBAAgB;YAChB;QACF,GAAG;IACL;IAEA,MAAM,sBAAsB,CAAC;QAC3B,IAAI,EAAE,MAAM,KAAK,EAAE,aAAa,EAAE;YAChC;QACF;IACF;IAEA,IAAI,CAAC,WAAW,OAAO;IAEvB,qBACE,8OAAC;QACC,WAAW,CAAC,+CAA+C,EACzD,cAAc,gBAAgB,cAC9B;QACF,SAAS;;0BAGT,8OAAC;gBACC,WAAW,CAAC,qHAAqH,EAC/H,cAAc,kBAAkB,oBAChC;gBACF,OAAO;oBAAE,WAAW;gBAAO;;kCAG3B,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CACX,SAAS,YAAY,gBAAgB;;;;;;0CAExC,8OAAC;gCACC,SAAS;gCACT,WAAU;0CAEV,cAAA,8OAAC,4LAAA,CAAA,IAAC;oCAAC,MAAM;;;;;;;;;;;;;;;;;kCAKb,8OAAC;wBAAI,WAAU;wBAAsB,OAAO;4BAAE,WAAW;wBAAoB;kCAC1E,SAAS,0BACR,8OAAC;4BAAY,UAAU;;;;;iDAEvB,8OAAC;4BAAW,UAAU;;;;;;;;;;;;;;;;;0BAM5B,8OAAC;gBACC,WAAW,CAAC,iIAAiI,EAC3I,cAAc,kBAAkB,oBAChC;;kCAGF,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CACX,SAAS,YAAY,gBAAgB;;;;;;0CAExC,8OAAC;gCACC,SAAS;gCACT,WAAU;0CAEV,cAAA,8OAAC,4LAAA,CAAA,IAAC;oCAAC,MAAM;;;;;;;;;;;;;;;;;kCAKb,8OAAC;wBAAI,WAAU;kCACZ,SAAS,0BACR,8OAAC;4BAAY,UAAU;;;;;iDAEvB,8OAAC;4BAAW,UAAU;;;;;;;;;;;;;;;;;;;;;;;AAMlC;AAEA,SAAS,YAAY,EAAE,QAAQ,EAA4B;IACzD,MAAM,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,6HAAA,CAAA,SAAM,AAAD;IACvB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,UAAU;QACV,aAAa;QACb,MAAM;QACN,MAAM;QACN,OAAO;QACP,OAAO;IACT;IAEA,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAEpE,MAAM,qBAAqB,CAAC;QAC1B,MAAM,SAAS,MAAM,OAAO,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;QAChD,kBAAkB,UAAU;QAC5B,YAAY;YACV,GAAG,QAAQ;YACX;YACA,OAAO,CAAA,GAAA,uHAAA,CAAA,iBAAc,AAAD,EAAE,SAAS,WAAW,EAAE,QAAQ,cAAc;QACpE;IACF;IAEA,MAAM,0BAA0B,CAAC;QAC/B,YAAY;YACV,GAAG,QAAQ;YACX;YACA,OAAO,CAAA,GAAA,uHAAA,CAAA,iBAAc,AAAD,EAAE,aAAa,gBAAgB,cAAc;QACnE;IACF;IAEA,MAAM,eAAe,CAAC;QACpB,EAAE,cAAc;QAChB,mCAAmC;QACnC,QAAQ,GAAG,CAAC,qBAAqB;QACjC;IACF;IAEA,qBACE,8OAAC;QAAK,UAAU;QAAc,WAAU;;0BACtC,8OAAC;;kCACC,8OAAC;wBAAM,WAAU;kCAA+C;;;;;;kCAGhE,8OAAC;wBACC,OAAO,SAAS,QAAQ;wBACxB,UAAU,CAAC,IAAM,mBAAmB,EAAE,MAAM,CAAC,KAAK;wBAClD,WAAU;wBACV,QAAQ;;0CAER,8OAAC;gCAAO,OAAM;0CAAG;;;;;;4BAChB,MAAM,OAAO,CAAC,GAAG,CAAC,CAAA,uBACjB,8OAAC;oCAAuB,OAAO,OAAO,EAAE;;wCACrC,OAAO,SAAS;wCAAC;wCAAE,OAAO,QAAQ;wCAAE,OAAO,OAAO,GAAG,CAAC,IAAI,EAAE,OAAO,OAAO,EAAE,GAAG;;mCADrE,OAAO,EAAE;;;;;;;;;;;;;;;;;0BAO5B,8OAAC;;kCACC,8OAAC;wBAAM,WAAU;kCAA+C;;;;;;kCAGhE,8OAAC;wBACC,OAAO,SAAS,WAAW;wBAC3B,UAAU,CAAC,IAAM,wBAAwB,EAAE,MAAM,CAAC,KAAK;wBACvD,WAAU;;0CAEV,8OAAC;gCAAO,OAAM;0CAAY;;;;;;0CAC1B,8OAAC;gCAAO,OAAM;0CAAS;;;;;;0CACvB,8OAAC;gCAAO,OAAM;0CAAW;;;;;;0CACzB,8OAAC;gCAAO,OAAM;0CAAiB;;;;;;0CAC/B,8OAAC;gCAAO,OAAM;0CAAQ;;;;;;0CACtB,8OAAC;gCAAO,OAAM;0CAAa;;;;;;0CAC3B,8OAAC;gCAAO,OAAM;0CAAW;;;;;;;;;;;;;;;;;;0BAI7B,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;;0CACC,8OAAC;gCAAM,WAAU;0CAA+C;;;;;;0CAGhE,8OAAC;gCACC,MAAK;gCACL,OAAO,SAAS,IAAI;gCACpB,UAAU,CAAC,IAAM,YAAY;wCAAE,GAAG,QAAQ;wCAAE,MAAM,EAAE,MAAM,CAAC,KAAK;oCAAC;gCACjE,WAAU;gCACV,QAAQ;;;;;;;;;;;;kCAIZ,8OAAC;;0CACC,8OAAC;gCAAM,WAAU;0CAA+C;;;;;;0CAGhE,8OAAC;gCACC,MAAK;gCACL,OAAO,SAAS,IAAI;gCACpB,UAAU,CAAC,IAAM,YAAY;wCAAE,GAAG,QAAQ;wCAAE,MAAM,EAAE,MAAM,CAAC,KAAK;oCAAC;gCACjE,WAAU;gCACV,QAAQ;;;;;;;;;;;;;;;;;;0BAKd,8OAAC;;kCACC,8OAAC;wBAAM,WAAU;kCAA+C;;;;;;kCAGhE,8OAAC;wBACC,MAAK;wBACL,OAAO,SAAS,KAAK;wBACrB,UAAU,CAAC,IAAM,YAAY;gCAAE,GAAG,QAAQ;gCAAE,OAAO,WAAW,EAAE,MAAM,CAAC,KAAK,KAAK;4BAAE;wBACnF,WAAU;wBACV,aAAY;wBACZ,KAAI;wBACJ,MAAK;wBACL,QAAQ;;;;;;oBAET,gCACC,8OAAC;wBAAE,WAAU;;4BAA6B;4BACrB,CAAA,GAAA,uHAAA,CAAA,iBAAc,AAAD,EAAE,SAAS,WAAW,EAAE,eAAe,UAAU;4BAChF,eAAe,UAAU,GAAG,cAAc;;;;;;;;;;;;;0BAKjD,8OAAC;;kCACC,8OAAC;wBAAM,WAAU;kCAA+C;;;;;;kCAGhE,8OAAC;wBACC,OAAO,SAAS,KAAK;wBACrB,UAAU,CAAC,IAAM,YAAY;gCAAE,GAAG,QAAQ;gCAAE,OAAO,EAAE,MAAM,CAAC,KAAK;4BAAC;wBAClE,WAAU;wBACV,aAAY;wBACZ,MAAM;;;;;;;;;;;;0BAIV,8OAAC;gBACC,MAAK;gBACL,WAAU;0BACX;;;;;;;;;;;;AAKP;AAEA,SAAS,WAAW,EAAE,QAAQ,EAA4B;IACxD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,WAAW;QACX,UAAU;QACV,SAAS;QACT,WAAW;YAAC;SAAG;QACf,OAAO;QACP,OAAO;QACP,SAAS;QACT,QAAQ;QACR,YAAY;IACd;IAEA,MAAM,eAAe,CAAC;QACpB,EAAE,cAAc;QAChB,kCAAkC;QAClC,QAAQ,GAAG,CAAC,oBAAoB;QAChC;IACF;IAEA,MAAM,cAAc;QAClB,YAAY;YAAE,GAAG,QAAQ;YAAE,WAAW;mBAAI,SAAS,SAAS;gBAAE;aAAG;QAAC;IACpE;IAEA,MAAM,iBAAiB,CAAC;QACtB,MAAM,eAAe,SAAS,SAAS,CAAC,MAAM,CAAC,CAAC,GAAG,IAAM,MAAM;QAC/D,YAAY;YAAE,GAAG,QAAQ;YAAE,WAAW;QAAa;IACrD;IAEA,MAAM,iBAAiB,CAAC,OAAe;QACrC,MAAM,eAAe;eAAI,SAAS,SAAS;SAAC;QAC5C,YAAY,CAAC,MAAM,GAAG;QACtB,YAAY;YAAE,GAAG,QAAQ;YAAE,WAAW;QAAa;IACrD;IAEA,qBACE,8OAAC;QAAK,UAAU;QAAc,WAAU;;0BACtC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;;0CACC,8OAAC;gCAAM,WAAU;0CAA+C;;;;;;0CAGhE,8OAAC;gCACC,MAAK;gCACL,OAAO,SAAS,SAAS;gCACzB,UAAU,CAAC,IAAM,YAAY;wCAAE,GAAG,QAAQ;wCAAE,WAAW,EAAE,MAAM,CAAC,KAAK;oCAAC;gCACtE,WAAU;gCACV,aAAY;gCACZ,QAAQ;;;;;;;;;;;;kCAGZ,8OAAC;;0CACC,8OAAC;gCAAM,WAAU;0CAA+C;;;;;;0CAGhE,8OAAC;gCACC,MAAK;gCACL,OAAO,SAAS,QAAQ;gCACxB,UAAU,CAAC,IAAM,YAAY;wCAAE,GAAG,QAAQ;wCAAE,UAAU,EAAE,MAAM,CAAC,KAAK;oCAAC;gCACrE,WAAU;gCACV,aAAY;gCACZ,QAAQ;;;;;;;;;;;;;;;;;;0BAKd,8OAAC;;kCACC,8OAAC;wBAAM,WAAU;kCAA+C;;;;;;kCAGhE,8OAAC;wBACC,MAAK;wBACL,OAAO,SAAS,OAAO;wBACvB,UAAU,CAAC,IAAM,YAAY;gCAAE,GAAG,QAAQ;gCAAE,SAAS,EAAE,MAAM,CAAC,KAAK;4BAAC;wBACpE,WAAU;wBACV,aAAY;wBACZ,QAAQ;;;;;;;;;;;;0BAIZ,8OAAC;;kCACC,8OAAC;wBAAM,WAAU;kCAA+C;;;;;;oBAG/D,SAAS,SAAS,CAAC,GAAG,CAAC,CAAC,KAAK,sBAC5B,8OAAC;4BAAgB,WAAU;;8CACzB,8OAAC;oCACC,MAAK;oCACL,OAAO;oCACP,UAAU,CAAC,IAAM,eAAe,OAAO,EAAE,MAAM,CAAC,KAAK;oCACrD,WAAU;oCACV,aAAY;;;;;;gCAEb,SAAS,SAAS,CAAC,MAAM,GAAG,mBAC3B,8OAAC;oCACC,MAAK;oCACL,SAAS,IAAM,eAAe;oCAC9B,WAAU;8CACX;;;;;;;2BAbK;;;;;kCAmBZ,8OAAC;wBACC,MAAK;wBACL,SAAS;wBACT,WAAU;kCACX;;;;;;;;;;;;0BAKH,8OAAC;;kCACC,8OAAC;wBAAM,WAAU;kCAA+C;;;;;;kCAGhE,8OAAC;wBACC,MAAK;wBACL,OAAO,SAAS,KAAK;wBACrB,UAAU,CAAC,IAAM,YAAY;gCAAE,GAAG,QAAQ;gCAAE,OAAO,EAAE,MAAM,CAAC,KAAK;4BAAC;wBAClE,WAAU;wBACV,aAAY;;;;;;;;;;;;0BAIhB,8OAAC;;kCACC,8OAAC;wBAAM,WAAU;kCAA+C;;;;;;kCAGhE,8OAAC;wBACC,MAAK;wBACL,OAAO,SAAS,KAAK;wBACrB,UAAU,CAAC,IAAM,YAAY;gCAAE,GAAG,QAAQ;gCAAE,OAAO,EAAE,MAAM,CAAC,KAAK;4BAAC;wBAClE,WAAU;wBACV,aAAY;;;;;;;;;;;;0BAIhB,8OAAC;;kCACC,8OAAC;wBAAM,WAAU;kCAA+C;;;;;;kCAGhE,8OAAC;wBACC,OAAO,SAAS,OAAO;wBACvB,UAAU,CAAC,IAAM,YAAY;gCAAE,GAAG,QAAQ;gCAAE,SAAS,EAAE,MAAM,CAAC,KAAK;4BAAC;wBACpE,WAAU;wBACV,aAAY;wBACZ,MAAM;;;;;;;;;;;;0BAIV,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAM,WAAU;0CAAoC;;;;;;0CAGrD,8OAAC;gCACC,MAAK;gCACL,SAAS,IAAM,YAAY;wCAAE,GAAG,QAAQ;wCAAE,QAAQ,CAAC,SAAS,MAAM;oCAAC;gCACnE,WAAW,CAAC,0EAA0E,EACpF,SAAS,MAAM,GAAG,iBAAiB,eACnC;0CAEF,cAAA,8OAAC;oCACC,WAAW,CAAC,0EAA0E,EACpF,SAAS,MAAM,GAAG,kBAAkB,iBACpC;;;;;;;;;;;;;;;;;kCAIR,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAM,WAAU;0CAAoC;;;;;;0CAGrD,8OAAC;gCACC,MAAK;gCACL,SAAS,IAAM,YAAY;wCAAE,GAAG,QAAQ;wCAAE,YAAY,CAAC,SAAS,UAAU;oCAAC;gCAC3E,WAAW,CAAC,0EAA0E,EACpF,SAAS,UAAU,GAAG,iBAAiB,eACvC;0CAEF,cAAA,8OAAC;oCACC,WAAW,CAAC,0EAA0E,EACpF,SAAS,UAAU,GAAG,kBAAkB,iBACxC;;;;;;;;;;;;;;;;;;;;;;;0BAMV,8OAAC;gBACC,MAAK;gBACL,WAAU;0BACX;;;;;;;;;;;;AAKP", "debugId": null}}, {"offset": {"line": 2344, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/RMR%20App%20Version%202/raising-my-rescue/src/app/calendar/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { useApp } from '@/context/AppContext';\nimport Header from '@/components/layout/Header';\nimport SessionModal from '@/components/modals/SessionModal';\nimport EditSessionModal from '@/components/modals/EditSessionModal';\nimport EditClientModal from '@/components/modals/EditClientModal';\nimport AddModal from '@/components/AddModal';\nimport { Session, Client } from '@/types';\nimport { format, startOfMonth, endOfMonth, eachDayOfInterval, isSameDay, addMonths, subMonths } from 'date-fns';\nimport { ChevronLeft, ChevronRight, Calendar, UserPlus } from 'lucide-react';\n\nexport default function CalendarPage() {\n  const { state } = useApp();\n  const [currentDate, setCurrentDate] = useState(new Date());\n  const [selectedSession, setSelectedSession] = useState<Session | null>(null);\n  const [showAddModal, setShowAddModal] = useState(false);\n  const [addModalType, setAddModalType] = useState<'session' | 'client'>('session');\n  const [showEditSessionModal, setShowEditSessionModal] = useState(false);\n  const [showEditClientModal, setShowEditClientModal] = useState(false);\n  const [editingClient, setEditingClient] = useState<Client | null>(null);\n\n  const monthStart = startOfMonth(currentDate);\n  const monthEnd = endOfMonth(currentDate);\n  const daysInMonth = eachDayOfInterval({ start: monthStart, end: monthEnd });\n\n  const getSessionsForDay = (day: Date) => {\n    return state.sessions.filter(session =>\n      isSameDay(new Date(session.bookingDate), day)\n    );\n  };\n\n  const handlePreviousMonth = () => {\n    setCurrentDate(subMonths(currentDate, 1));\n  };\n\n  const handleNextMonth = () => {\n    setCurrentDate(addMonths(currentDate, 1));\n  };\n\n  const handleSessionClick = (session: Session) => {\n    setSelectedSession(session);\n  };\n\n  const handleCloseModal = () => {\n    setSelectedSession(null);\n  };\n\n  const handleAddSession = () => {\n    setAddModalType('session');\n    setShowAddModal(true);\n  };\n\n  const handleAddClient = () => {\n    setAddModalType('client');\n    setShowAddModal(true);\n  };\n\n  const handleCloseAddModal = () => {\n    setShowAddModal(false);\n  };\n\n  const handleEditSession = (session: Session) => {\n    setSelectedSession(session);\n    setShowEditSessionModal(true);\n  };\n\n  const handleEditClient = (session: Session) => {\n    // Find the client based on the session's owner name\n    const client = state.clients.find(c => c.ownerName === session.ownerName);\n    if (client) {\n      setEditingClient(client);\n      setShowEditClientModal(true);\n    }\n  };\n\n  const handleCloseEditSessionModal = () => {\n    setShowEditSessionModal(false);\n  };\n\n  const handleCloseEditClientModal = () => {\n    setShowEditClientModal(false);\n    setEditingClient(null);\n  };\n\n  const handleUpNextClick = () => {\n    if (firstSession) {\n      setSelectedSession(firstSession);\n    }\n  };\n\n  // Keyboard navigation for months\n  useEffect(() => {\n    const handleKeyDown = (event: KeyboardEvent) => {\n      if (event.key === 'ArrowLeft') {\n        event.preventDefault();\n        handlePreviousMonth();\n      } else if (event.key === 'ArrowRight') {\n        event.preventDefault();\n        handleNextMonth();\n      }\n    };\n\n    window.addEventListener('keydown', handleKeyDown);\n    return () => window.removeEventListener('keydown', handleKeyDown);\n  }, []);\n\n  // Focus the calendar container to enable keyboard navigation\n  useEffect(() => {\n    const calendarContainer = document.getElementById('calendar-container');\n    if (calendarContainer) {\n      calendarContainer.focus();\n    }\n  }, []);\n\n  // Get the first session for the bottom preview\n  const firstSession = state.sessions[0];\n\n  return (\n    <div\n      id=\"calendar-container\"\n      className=\"h-screen bg-white flex flex-col overflow-hidden outline-none\"\n      tabIndex={0}\n    >\n      <div className=\"bg-amber-800\">\n        <Header\n          title=\"Calendar\"\n          buttons={[\n            {\n              icon: Calendar,\n              onClick: handleAddSession,\n              title: 'Add Session'\n            },\n            {\n              icon: UserPlus,\n              onClick: handleAddClient,\n              title: 'Add Client'\n            }\n          ]}\n          showSearch\n          searchPlaceholder=\"Search\"\n        />\n      </div>\n\n      {/* Calendar Section - Flex-1 to take remaining space */}\n      <div className=\"bg-white flex flex-col flex-1 overflow-hidden\">\n        {/* Month Navigation */}\n        <div className=\"px-4 py-4 border-b border-gray-200 flex-shrink-0\">\n          <div className=\"flex items-center justify-between\">\n            <h2 className=\"text-lg font-semibold\">\n              {format(currentDate, 'MMM yyyy')}\n            </h2>\n            <div className=\"flex items-center gap-2\">\n              <button\n                onClick={handlePreviousMonth}\n                className=\"p-2 hover:bg-gray-100 rounded transition-colors\"\n              >\n                <ChevronLeft size={20} />\n              </button>\n              <button\n                onClick={handleNextMonth}\n                className=\"p-2 hover:bg-gray-100 rounded transition-colors\"\n              >\n                <ChevronRight size={20} />\n              </button>\n            </div>\n          </div>\n        </div>\n\n        {/* Calendar Grid - Fills remaining space */}\n        <div className=\"flex-1 px-4 py-2 flex flex-col min-h-0 overflow-hidden\">\n          <div className=\"grid grid-cols-7 gap-1 mb-2 flex-shrink-0\">\n            {['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'].map(day => (\n              <div key={day} className=\"text-center text-sm font-medium text-gray-500 py-2\">\n                {day}\n              </div>\n            ))}\n          </div>\n\n          <div className=\"grid grid-cols-7 gap-1 flex-1 min-h-0 auto-rows-fr\">\n            {daysInMonth.map(day => {\n              const sessions = getSessionsForDay(day);\n              const dayNumber = format(day, 'd');\n\n              return (\n                <div key={day.toISOString()} className=\"flex flex-col p-1 min-h-0 border-r border-b border-gray-100 last:border-r-0\">\n                  <div className=\"text-sm font-medium mb-1 flex-shrink-0\">{dayNumber}</div>\n                  <div className=\"space-y-1 flex-1 min-h-0 overflow-hidden\">\n                    {sessions.slice(0, 2).map(session => (\n                      <button\n                        key={session.id}\n                        onClick={() => handleSessionClick(session)}\n                        className=\"w-full bg-amber-800 text-white text-xs px-2 py-1 rounded text-left hover:bg-amber-700 transition-colors flex-shrink-0\"\n                      >\n                        {format(new Date(session.bookingDate), 'HH:mm')}...\n                      </button>\n                    ))}\n                    {sessions.length > 2 && (\n                      <div className=\"text-xs text-amber-800 font-medium flex-shrink-0\">\n                        +{sessions.length - 2} more\n                      </div>\n                    )}\n                  </div>\n                </div>\n              );\n            })}\n          </div>\n        </div>\n      </div>\n\n      {/* Up Next Section - Fixed at bottom */}\n      <button\n        onClick={handleUpNextClick}\n        disabled={!firstSession}\n        className=\"bg-amber-800 text-white px-4 py-4 flex-shrink-0 w-full text-left disabled:cursor-default\"\n        style={{ paddingBottom: 'max(env(safe-area-inset-bottom), 20px)' }}\n      >\n        {firstSession ? (\n          <>\n            <div className=\"text-lg font-medium\">\n              {format(new Date(firstSession.bookingDate), 'HH:mm')} | {firstSession.ownerName} w/ {firstSession.dogName}\n            </div>\n            <div className=\"text-white/80 text-sm\">\n              {firstSession.sessionType} • {format(new Date(firstSession.bookingDate), 'EEEE, d MMMM yyyy')}\n            </div>\n          </>\n        ) : (\n          <div className=\"text-lg font-medium\">No upcoming sessions</div>\n        )}\n      </button>\n\n      <SessionModal\n        session={selectedSession}\n        isOpen={!!selectedSession && !showEditSessionModal && !showEditClientModal}\n        onClose={handleCloseModal}\n        onEditSession={handleEditSession}\n        onEditClient={handleEditClient}\n      />\n\n      <EditSessionModal\n        session={selectedSession}\n        isOpen={showEditSessionModal}\n        onClose={handleCloseEditSessionModal}\n      />\n\n      <EditClientModal\n        client={editingClient}\n        isOpen={showEditClientModal}\n        onClose={handleCloseEditClientModal}\n      />\n\n      <AddModal\n        isOpen={showAddModal}\n        onClose={handleCloseAddModal}\n        type={addModalType}\n      />\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAXA;;;;;;;;;;;AAae,SAAS;IACtB,MAAM,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,6HAAA,CAAA,SAAM,AAAD;IACvB,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,IAAI;IACnD,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAkB;IACvE,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAwB;IACvE,MAAM,CAAC,sBAAsB,wBAAwB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjE,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/D,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAElE,MAAM,aAAa,CAAA,GAAA,2IAAA,CAAA,eAAY,AAAD,EAAE;IAChC,MAAM,WAAW,CAAA,GAAA,yIAAA,CAAA,aAAU,AAAD,EAAE;IAC5B,MAAM,cAAc,CAAA,GAAA,gJAAA,CAAA,oBAAiB,AAAD,EAAE;QAAE,OAAO;QAAY,KAAK;IAAS;IAEzE,MAAM,oBAAoB,CAAC;QACzB,OAAO,MAAM,QAAQ,CAAC,MAAM,CAAC,CAAA,UAC3B,CAAA,GAAA,wIAAA,CAAA,YAAS,AAAD,EAAE,IAAI,KAAK,QAAQ,WAAW,GAAG;IAE7C;IAEA,MAAM,sBAAsB;QAC1B,eAAe,CAAA,GAAA,wIAAA,CAAA,YAAS,AAAD,EAAE,aAAa;IACxC;IAEA,MAAM,kBAAkB;QACtB,eAAe,CAAA,GAAA,wIAAA,CAAA,YAAS,AAAD,EAAE,aAAa;IACxC;IAEA,MAAM,qBAAqB,CAAC;QAC1B,mBAAmB;IACrB;IAEA,MAAM,mBAAmB;QACvB,mBAAmB;IACrB;IAEA,MAAM,mBAAmB;QACvB,gBAAgB;QAChB,gBAAgB;IAClB;IAEA,MAAM,kBAAkB;QACtB,gBAAgB;QAChB,gBAAgB;IAClB;IAEA,MAAM,sBAAsB;QAC1B,gBAAgB;IAClB;IAEA,MAAM,oBAAoB,CAAC;QACzB,mBAAmB;QACnB,wBAAwB;IAC1B;IAEA,MAAM,mBAAmB,CAAC;QACxB,oDAAoD;QACpD,MAAM,SAAS,MAAM,OAAO,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE,SAAS,KAAK,QAAQ,SAAS;QACxE,IAAI,QAAQ;YACV,iBAAiB;YACjB,uBAAuB;QACzB;IACF;IAEA,MAAM,8BAA8B;QAClC,wBAAwB;IAC1B;IAEA,MAAM,6BAA6B;QACjC,uBAAuB;QACvB,iBAAiB;IACnB;IAEA,MAAM,oBAAoB;QACxB,IAAI,cAAc;YAChB,mBAAmB;QACrB;IACF;IAEA,iCAAiC;IACjC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,gBAAgB,CAAC;YACrB,IAAI,MAAM,GAAG,KAAK,aAAa;gBAC7B,MAAM,cAAc;gBACpB;YACF,OAAO,IAAI,MAAM,GAAG,KAAK,cAAc;gBACrC,MAAM,cAAc;gBACpB;YACF;QACF;QAEA,OAAO,gBAAgB,CAAC,WAAW;QACnC,OAAO,IAAM,OAAO,mBAAmB,CAAC,WAAW;IACrD,GAAG,EAAE;IAEL,6DAA6D;IAC7D,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,oBAAoB,SAAS,cAAc,CAAC;QAClD,IAAI,mBAAmB;YACrB,kBAAkB,KAAK;QACzB;IACF,GAAG,EAAE;IAEL,+CAA+C;IAC/C,MAAM,eAAe,MAAM,QAAQ,CAAC,EAAE;IAEtC,qBACE,8OAAC;QACC,IAAG;QACH,WAAU;QACV,UAAU;;0BAEV,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,sIAAA,CAAA,UAAM;oBACL,OAAM;oBACN,SAAS;wBACP;4BACE,MAAM,0MAAA,CAAA,WAAQ;4BACd,SAAS;4BACT,OAAO;wBACT;wBACA;4BACE,MAAM,8MAAA,CAAA,WAAQ;4BACd,SAAS;4BACT,OAAO;wBACT;qBACD;oBACD,UAAU;oBACV,mBAAkB;;;;;;;;;;;0BAKtB,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CACX,CAAA,GAAA,qJAAA,CAAA,SAAM,AAAD,EAAE,aAAa;;;;;;8CAEvB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CACC,SAAS;4CACT,WAAU;sDAEV,cAAA,8OAAC,oNAAA,CAAA,cAAW;gDAAC,MAAM;;;;;;;;;;;sDAErB,8OAAC;4CACC,SAAS;4CACT,WAAU;sDAEV,cAAA,8OAAC,sNAAA,CAAA,eAAY;gDAAC,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAO5B,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACZ;oCAAC;oCAAO;oCAAO;oCAAO;oCAAO;oCAAO;oCAAO;iCAAM,CAAC,GAAG,CAAC,CAAA,oBACrD,8OAAC;wCAAc,WAAU;kDACtB;uCADO;;;;;;;;;;0CAMd,8OAAC;gCAAI,WAAU;0CACZ,YAAY,GAAG,CAAC,CAAA;oCACf,MAAM,WAAW,kBAAkB;oCACnC,MAAM,YAAY,CAAA,GAAA,qJAAA,CAAA,SAAM,AAAD,EAAE,KAAK;oCAE9B,qBACE,8OAAC;wCAA4B,WAAU;;0DACrC,8OAAC;gDAAI,WAAU;0DAA0C;;;;;;0DACzD,8OAAC;gDAAI,WAAU;;oDACZ,SAAS,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAA,wBACxB,8OAAC;4DAEC,SAAS,IAAM,mBAAmB;4DAClC,WAAU;;gEAET,CAAA,GAAA,qJAAA,CAAA,SAAM,AAAD,EAAE,IAAI,KAAK,QAAQ,WAAW,GAAG;gEAAS;;2DAJ3C,QAAQ,EAAE;;;;;oDAOlB,SAAS,MAAM,GAAG,mBACjB,8OAAC;wDAAI,WAAU;;4DAAmD;4DAC9D,SAAS,MAAM,GAAG;4DAAE;;;;;;;;;;;;;;uCAdpB,IAAI,WAAW;;;;;gCAoB7B;;;;;;;;;;;;;;;;;;0BAMN,8OAAC;gBACC,SAAS;gBACT,UAAU,CAAC;gBACX,WAAU;gBACV,OAAO;oBAAE,eAAe;gBAAyC;0BAEhE,6BACC;;sCACE,8OAAC;4BAAI,WAAU;;gCACZ,CAAA,GAAA,qJAAA,CAAA,SAAM,AAAD,EAAE,IAAI,KAAK,aAAa,WAAW,GAAG;gCAAS;gCAAI,aAAa,SAAS;gCAAC;gCAAK,aAAa,OAAO;;;;;;;sCAE3G,8OAAC;4BAAI,WAAU;;gCACZ,aAAa,WAAW;gCAAC;gCAAI,CAAA,GAAA,qJAAA,CAAA,SAAM,AAAD,EAAE,IAAI,KAAK,aAAa,WAAW,GAAG;;;;;;;;iDAI7E,8OAAC;oBAAI,WAAU;8BAAsB;;;;;;;;;;;0BAIzC,8OAAC,4IAAA,CAAA,UAAY;gBACX,SAAS;gBACT,QAAQ,CAAC,CAAC,mBAAmB,CAAC,wBAAwB,CAAC;gBACvD,SAAS;gBACT,eAAe;gBACf,cAAc;;;;;;0BAGhB,8OAAC,gJAAA,CAAA,UAAgB;gBACf,SAAS;gBACT,QAAQ;gBACR,SAAS;;;;;;0BAGX,8OAAC,+IAAA,CAAA,UAAe;gBACd,QAAQ;gBACR,QAAQ;gBACR,SAAS;;;;;;0BAGX,8OAAC,8HAAA,CAAA,UAAQ;gBACP,QAAQ;gBACR,SAAS;gBACT,MAAM;;;;;;;;;;;;AAId", "debugId": null}}]}