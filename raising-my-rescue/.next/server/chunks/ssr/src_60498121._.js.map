{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/RMR%20App%20Version%202/raising-my-rescue/src/components/layout/Header.tsx"], "sourcesContent": ["'use client';\n\nimport { Search, Plus } from 'lucide-react';\nimport { useState } from 'react';\n\ninterface HeaderProps {\n  title: string;\n  showAddButton?: boolean;\n  onAddClick?: () => void;\n  addButtonText?: string;\n  showSearch?: boolean;\n  onSearch?: (query: string) => void;\n  searchPlaceholder?: string;\n}\n\nexport default function Header({\n  title,\n  showAddButton = false,\n  onAddClick,\n  addButtonText = 'Add',\n  showSearch = false,\n  onSearch,\n  searchPlaceholder = 'Search',\n}: HeaderProps) {\n  const [searchQuery, setSearchQuery] = useState('');\n\n  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {\n    const query = e.target.value;\n    setSearchQuery(query);\n    onSearch?.(query);\n  };\n\n  return (\n    <div className=\"bg-amber-800 text-white px-4 py-3 safe-area-pt\">\n      {/* Top row with title and buttons */}\n      <div className=\"flex items-center justify-between mb-3\">\n        <h1 className=\"text-3xl font-semibold\">{title}</h1>\n        <div className=\"flex items-center gap-2\">\n          {showAddButton && (\n            <button\n              onClick={onAddClick}\n              className=\"bg-white/20 hover:bg-white/30 p-2 rounded-full transition-colors\"\n              title={addButtonText}\n            >\n              <Plus size={20} />\n            </button>\n          )}\n        </div>\n      </div>\n\n      {/* Search bar */}\n      {showSearch && (\n        <div className=\"relative\">\n          <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-white/60\" size={20} />\n          <input\n            type=\"text\"\n            value={searchQuery}\n            onChange={handleSearchChange}\n            placeholder={searchPlaceholder}\n            className=\"w-full bg-white/20 placeholder-white/60 text-white px-10 py-3 rounded-lg focus:outline-none focus:bg-white/30 transition-colors\"\n          />\n        </div>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AACA;AAHA;;;;AAee,SAAS,OAAO,EAC7B,KAAK,EACL,gBAAgB,KAAK,EACrB,UAAU,EACV,gBAAgB,KAAK,EACrB,aAAa,KAAK,EAClB,QAAQ,EACR,oBAAoB,QAAQ,EAChB;IACZ,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,MAAM,qBAAqB,CAAC;QAC1B,MAAM,QAAQ,EAAE,MAAM,CAAC,KAAK;QAC5B,eAAe;QACf,WAAW;IACb;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAA0B;;;;;;kCACxC,8OAAC;wBAAI,WAAU;kCACZ,+BACC,8OAAC;4BACC,SAAS;4BACT,WAAU;4BACV,OAAO;sCAEP,cAAA,8OAAC,kMAAA,CAAA,OAAI;gCAAC,MAAM;;;;;;;;;;;;;;;;;;;;;;YAOnB,4BACC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,sMAAA,CAAA,SAAM;wBAAC,WAAU;wBAAmE,MAAM;;;;;;kCAC3F,8OAAC;wBACC,MAAK;wBACL,OAAO;wBACP,UAAU;wBACV,aAAa;wBACb,WAAU;;;;;;;;;;;;;;;;;;AAMtB", "debugId": null}}, {"offset": {"line": 109, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/RMR%20App%20Version%202/raising-my-rescue/src/components/modals/SlideUpModal.tsx"], "sourcesContent": ["'use client';\n\nimport { ReactNode, useEffect, useState } from 'react';\nimport { X } from 'lucide-react';\nimport { useModal } from '@/context/ModalContext';\n\ninterface SlideUpModalProps {\n  isOpen: boolean;\n  onClose: () => void;\n  title: string;\n  children: ReactNode;\n}\n\nexport default function SlideUpModal({ isOpen, onClose, title, children }: SlideUpModalProps) {\n  const { registerModal, unregisterModal } = useModal();\n  const [isVisible, setIsVisible] = useState(false);\n  const [isAnimating, setIsAnimating] = useState(false);\n  const [modalId] = useState(() => `slide-up-modal-${Math.random().toString(36).substr(2, 9)}`);\n\n  useEffect(() => {\n    if (isOpen) {\n      setIsVisible(true);\n      document.body.style.overflow = 'hidden';\n      registerModal(modalId);\n      // Small delay to trigger animation\n      setTimeout(() => setIsAnimating(true), 10);\n    } else {\n      setIsAnimating(false);\n      // Wait for animation to complete before hiding\n      setTimeout(() => {\n        setIsVisible(false);\n        document.body.style.overflow = 'unset';\n        unregisterModal(modalId);\n      }, 300);\n    }\n\n    return () => {\n      document.body.style.overflow = 'unset';\n      unregisterModal(modalId);\n    };\n  }, [isOpen, registerModal, unregisterModal, modalId]);\n\n  const handleClose = () => {\n    setIsAnimating(false);\n    setTimeout(() => {\n      setIsVisible(false);\n      onClose();\n    }, 300);\n  };\n\n  const handleBackdropClick = (e: React.MouseEvent) => {\n    if (e.target === e.currentTarget) {\n      handleClose();\n    }\n  };\n\n  if (!isVisible) return null;\n\n  return (\n    <div\n      className={`fixed inset-0 z-50 transition-all duration-300 ${\n        isAnimating ? 'bg-black/50' : 'bg-black/0'\n      }`}\n      onClick={handleBackdropClick}\n    >\n      <div\n        className={`fixed bottom-0 left-0 right-0 bg-white text-black rounded-t-3xl shadow-2xl transition-transform duration-300 ease-out max-h-[85vh] overflow-hidden ${\n          isAnimating ? 'translate-y-0' : 'translate-y-full'\n        }`}\n      >\n        {/* Handle bar */}\n        <div className=\"flex justify-center py-3\">\n          <div className=\"w-12 h-1 bg-gray-300 rounded-full\" />\n        </div>\n\n        {/* Header */}\n        <div className=\"flex items-center justify-between px-6 pb-4\">\n          <h2 className=\"text-lg font-semibold\">{title}</h2>\n          <button\n            onClick={handleClose}\n            className=\"p-2 hover:bg-gray-100 rounded-full transition-colors\"\n          >\n            <X size={20} />\n          </button>\n        </div>\n\n        {/* Content */}\n        <div className=\"px-6 pb-6 overflow-y-auto max-h-[calc(85vh-120px)]\">\n          {children}\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAae,SAAS,aAAa,EAAE,MAAM,EAAE,OAAO,EAAE,KAAK,EAAE,QAAQ,EAAqB;IAC1F,MAAM,EAAE,aAAa,EAAE,eAAe,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,WAAQ,AAAD;IAClD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,IAAM,CAAC,eAAe,EAAE,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG,IAAI;IAE5F,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,QAAQ;YACV,aAAa;YACb,SAAS,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG;YAC/B,cAAc;YACd,mCAAmC;YACnC,WAAW,IAAM,eAAe,OAAO;QACzC,OAAO;YACL,eAAe;YACf,+CAA+C;YAC/C,WAAW;gBACT,aAAa;gBACb,SAAS,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG;gBAC/B,gBAAgB;YAClB,GAAG;QACL;QAEA,OAAO;YACL,SAAS,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG;YAC/B,gBAAgB;QAClB;IACF,GAAG;QAAC;QAAQ;QAAe;QAAiB;KAAQ;IAEpD,MAAM,cAAc;QAClB,eAAe;QACf,WAAW;YACT,aAAa;YACb;QACF,GAAG;IACL;IAEA,MAAM,sBAAsB,CAAC;QAC3B,IAAI,EAAE,MAAM,KAAK,EAAE,aAAa,EAAE;YAChC;QACF;IACF;IAEA,IAAI,CAAC,WAAW,OAAO;IAEvB,qBACE,8OAAC;QACC,WAAW,CAAC,+CAA+C,EACzD,cAAc,gBAAgB,cAC9B;QACF,SAAS;kBAET,cAAA,8OAAC;YACC,WAAW,CAAC,mJAAmJ,EAC7J,cAAc,kBAAkB,oBAChC;;8BAGF,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;;;;;;;;;;8BAIjB,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAyB;;;;;;sCACvC,8OAAC;4BACC,SAAS;4BACT,WAAU;sCAEV,cAAA,8OAAC,4LAAA,CAAA,IAAC;gCAAC,MAAM;;;;;;;;;;;;;;;;;8BAKb,8OAAC;oBAAI,WAAU;8BACZ;;;;;;;;;;;;;;;;;AAKX", "debugId": null}}, {"offset": {"line": 243, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/RMR%20App%20Version%202/raising-my-rescue/src/components/modals/SessionModal.tsx"], "sourcesContent": ["'use client';\n\nimport { Session } from '@/types';\nimport { useApp } from '@/context/AppContext';\nimport SlideUpModal from './SlideUpModal';\nimport { format } from 'date-fns';\n\ninterface SessionModalProps {\n  session: Session | null;\n  isOpen: boolean;\n  onClose: () => void;\n  onEditSession: (session: Session) => void;\n  onEditClient: (session: Session) => void;\n}\n\nexport default function SessionModal({ session, isOpen, onClose, onEditSession, onEditClient }: SessionModalProps) {\n  const { dispatch } = useApp();\n\n  if (!session) return null;\n\n  const handleDelete = () => {\n    dispatch({ type: 'DELETE_SESSION', payload: session.id });\n    onClose();\n  };\n\n  const handleTogglePaid = () => {\n    dispatch({\n      type: 'UPDATE_SESSION',\n      payload: { ...session, paid: !session.paid }\n    });\n  };\n\n  const handleToggleBehaviourPlan = () => {\n    dispatch({\n      type: 'UPDATE_SESSION',\n      payload: { ...session, behaviourPlanSent: !session.behaviourPlanSent }\n    });\n  };\n\n  const displayName = session.dogName \n    ? `${session.ownerName} w/ ${session.dogName}`\n    : session.ownerName;\n\n  return (\n    <SlideUpModal\n      isOpen={isOpen}\n      onClose={onClose}\n      title={displayName}\n    >\n      <div className=\"space-y-6\">\n        {/* Action Buttons */}\n        <div className=\"flex gap-3\">\n          <button\n            onClick={() => onEditSession(session)}\n            className=\"flex-1 bg-amber-800 hover:bg-amber-700 text-white py-3 px-4 rounded-lg font-medium transition-colors\"\n          >\n            Edit Session\n          </button>\n          <button\n            onClick={() => onEditClient(session)}\n            className=\"flex-1 bg-amber-800 hover:bg-amber-700 text-white py-3 px-4 rounded-lg font-medium transition-colors\"\n          >\n            Edit Client\n          </button>\n        </div>\n\n        {/* Session Details */}\n        <div className=\"space-y-4\">\n          <div className=\"flex justify-between items-center\">\n            <span className=\"text-gray-600\">Owner(s) Name</span>\n            <span className=\"font-medium text-gray-900\">{session.ownerName}</span>\n          </div>\n\n          {session.dogName && (\n            <div className=\"flex justify-between items-center\">\n              <span className=\"text-gray-600\">Dog(s) Name</span>\n              <span className=\"font-medium text-gray-900\">{session.dogName}</span>\n            </div>\n          )}\n\n          <div className=\"flex justify-between items-center\">\n            <span className=\"text-gray-600\">Booking</span>\n            <span className=\"font-medium text-gray-900\">\n              {format(session.bookingDate, 'dd/MM/yyyy, HH:mm')}\n            </span>\n          </div>\n\n          <div className=\"flex justify-between items-center\">\n            <span className=\"text-gray-600\">Session Type</span>\n            <span className=\"font-medium text-gray-900\">{session.sessionType}</span>\n          </div>\n\n          <div className=\"flex justify-between items-center\">\n            <span className=\"text-gray-600\">Quote</span>\n            <span className=\"font-medium text-gray-900\">£{session.quote}</span>\n          </div>\n\n          {/* Toggle Switches */}\n          <div className=\"flex justify-between items-center\">\n            <span className=\"text-gray-700 font-medium\">Paid</span>\n            <button\n              onClick={handleTogglePaid}\n              className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${\n                session.paid ? 'bg-green-500' : 'bg-gray-300'\n              }`}\n            >\n              <span\n                className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${\n                  session.paid ? 'translate-x-6' : 'translate-x-1'\n                }`}\n              />\n            </button>\n          </div>\n\n          <div className=\"flex justify-between items-center\">\n            <span className=\"text-gray-700 font-medium\">Behaviour Plan Sent</span>\n            <button\n              onClick={handleToggleBehaviourPlan}\n              className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${\n                session.behaviourPlanSent ? 'bg-green-500' : 'bg-gray-300'\n              }`}\n            >\n              <span\n                className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${\n                  session.behaviourPlanSent ? 'translate-x-6' : 'translate-x-1'\n                }`}\n              />\n            </button>\n          </div>\n        </div>\n\n        {/* Delete Button */}\n        <button\n          onClick={handleDelete}\n          className=\"w-full bg-amber-800 hover:bg-amber-700 text-white py-3 px-4 rounded-lg font-medium transition-colors mt-6\"\n        >\n          Delete Session\n        </button>\n      </div>\n    </SlideUpModal>\n  );\n}\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AALA;;;;;AAee,SAAS,aAAa,EAAE,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,aAAa,EAAE,YAAY,EAAqB;IAC/G,MAAM,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,6HAAA,CAAA,SAAM,AAAD;IAE1B,IAAI,CAAC,SAAS,OAAO;IAErB,MAAM,eAAe;QACnB,SAAS;YAAE,MAAM;YAAkB,SAAS,QAAQ,EAAE;QAAC;QACvD;IACF;IAEA,MAAM,mBAAmB;QACvB,SAAS;YACP,MAAM;YACN,SAAS;gBAAE,GAAG,OAAO;gBAAE,MAAM,CAAC,QAAQ,IAAI;YAAC;QAC7C;IACF;IAEA,MAAM,4BAA4B;QAChC,SAAS;YACP,MAAM;YACN,SAAS;gBAAE,GAAG,OAAO;gBAAE,mBAAmB,CAAC,QAAQ,iBAAiB;YAAC;QACvE;IACF;IAEA,MAAM,cAAc,QAAQ,OAAO,GAC/B,GAAG,QAAQ,SAAS,CAAC,IAAI,EAAE,QAAQ,OAAO,EAAE,GAC5C,QAAQ,SAAS;IAErB,qBACE,8OAAC,4IAAA,CAAA,UAAY;QACX,QAAQ;QACR,SAAS;QACT,OAAO;kBAEP,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BACC,SAAS,IAAM,cAAc;4BAC7B,WAAU;sCACX;;;;;;sCAGD,8OAAC;4BACC,SAAS,IAAM,aAAa;4BAC5B,WAAU;sCACX;;;;;;;;;;;;8BAMH,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAK,WAAU;8CAAgB;;;;;;8CAChC,8OAAC;oCAAK,WAAU;8CAA6B,QAAQ,SAAS;;;;;;;;;;;;wBAG/D,QAAQ,OAAO,kBACd,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAK,WAAU;8CAAgB;;;;;;8CAChC,8OAAC;oCAAK,WAAU;8CAA6B,QAAQ,OAAO;;;;;;;;;;;;sCAIhE,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAK,WAAU;8CAAgB;;;;;;8CAChC,8OAAC;oCAAK,WAAU;8CACb,CAAA,GAAA,qJAAA,CAAA,SAAM,AAAD,EAAE,QAAQ,WAAW,EAAE;;;;;;;;;;;;sCAIjC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAK,WAAU;8CAAgB;;;;;;8CAChC,8OAAC;oCAAK,WAAU;8CAA6B,QAAQ,WAAW;;;;;;;;;;;;sCAGlE,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAK,WAAU;8CAAgB;;;;;;8CAChC,8OAAC;oCAAK,WAAU;;wCAA4B;wCAAE,QAAQ,KAAK;;;;;;;;;;;;;sCAI7D,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAK,WAAU;8CAA4B;;;;;;8CAC5C,8OAAC;oCACC,SAAS;oCACT,WAAW,CAAC,0EAA0E,EACpF,QAAQ,IAAI,GAAG,iBAAiB,eAChC;8CAEF,cAAA,8OAAC;wCACC,WAAW,CAAC,0EAA0E,EACpF,QAAQ,IAAI,GAAG,kBAAkB,iBACjC;;;;;;;;;;;;;;;;;sCAKR,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAK,WAAU;8CAA4B;;;;;;8CAC5C,8OAAC;oCACC,SAAS;oCACT,WAAW,CAAC,0EAA0E,EACpF,QAAQ,iBAAiB,GAAG,iBAAiB,eAC7C;8CAEF,cAAA,8OAAC;wCACC,WAAW,CAAC,0EAA0E,EACpF,QAAQ,iBAAiB,GAAG,kBAAkB,iBAC9C;;;;;;;;;;;;;;;;;;;;;;;8BAOV,8OAAC;oBACC,SAAS;oBACT,WAAU;8BACX;;;;;;;;;;;;;;;;;AAMT", "debugId": null}}, {"offset": {"line": 546, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/RMR%20App%20Version%202/raising-my-rescue/src/components/modals/EditSessionModal.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { Session } from '@/types';\nimport { useApp } from '@/context/AppContext';\nimport SlideUpModal from './SlideUpModal';\nimport { format } from 'date-fns';\n\ninterface EditSessionModalProps {\n  session: Session | null;\n  isOpen: boolean;\n  onClose: () => void;\n}\n\nexport default function EditSessionModal({ session, isOpen, onClose }: EditSessionModalProps) {\n  const { dispatch } = useApp();\n  const [formData, setFormData] = useState({\n    ownerName: '',\n    dogName: '',\n    sessionType: 'In-Person',\n    date: '',\n    time: '',\n    quote: '',\n    paid: false,\n    behaviourPlanSent: false,\n    notes: ''\n  });\n\n  useEffect(() => {\n    if (session) {\n      const sessionDate = new Date(session.bookingDate);\n      setFormData({\n        ownerName: session.ownerName,\n        dogName: session.dogName || '',\n        sessionType: session.sessionType,\n        date: format(sessionDate, 'yyyy-MM-dd'),\n        time: format(sessionDate, 'HH:mm'),\n        quote: session.quote.toString(),\n        paid: session.paid,\n        behaviourPlanSent: session.behaviourPlanSent,\n        notes: session.notes || ''\n      });\n    }\n  }, [session]);\n\n  const handleSubmit = (e: React.FormEvent) => {\n    e.preventDefault();\n    if (!session) return;\n\n    const bookingDate = new Date(`${formData.date}T${formData.time}`);\n    \n    const updatedSession: Session = {\n      ...session,\n      ownerName: formData.ownerName,\n      dogName: formData.dogName || undefined,\n      sessionType: formData.sessionType as 'In-Person' | 'Online' | 'Group',\n      bookingDate,\n      quote: parseFloat(formData.quote),\n      paid: formData.paid,\n      behaviourPlanSent: formData.behaviourPlanSent,\n      notes: formData.notes || undefined\n    };\n\n    dispatch({ type: 'UPDATE_SESSION', payload: updatedSession });\n    onClose();\n  };\n\n  if (!session) return null;\n\n  return (\n    <SlideUpModal\n      isOpen={isOpen}\n      onClose={onClose}\n      title=\"Edit Session\"\n    >\n      <form onSubmit={handleSubmit} className=\"space-y-6\">\n        <div>\n          <label className=\"block text-gray-700 text-sm font-medium mb-2\">\n            Owner Name\n          </label>\n          <input\n            type=\"text\"\n            value={formData.ownerName}\n            onChange={(e) => setFormData({ ...formData, ownerName: e.target.value })}\n            className=\"w-full px-4 py-3 bg-gray-50 border border-gray-300 rounded-lg text-gray-900 placeholder-gray-500 focus:ring-2 focus:ring-amber-500 focus:border-transparent\"\n            placeholder=\"Enter owner name\"\n            required\n          />\n        </div>\n\n        <div>\n          <label className=\"block text-gray-700 text-sm font-medium mb-2\">\n            Dog Name (Optional)\n          </label>\n          <input\n            type=\"text\"\n            value={formData.dogName}\n            onChange={(e) => setFormData({ ...formData, dogName: e.target.value })}\n            className=\"w-full px-4 py-3 bg-gray-50 border border-gray-300 rounded-lg text-gray-900 placeholder-gray-500 focus:ring-2 focus:ring-amber-500 focus:border-transparent\"\n            placeholder=\"Enter dog name\"\n          />\n        </div>\n\n        <div>\n          <label className=\"block text-gray-700 text-sm font-medium mb-2\">\n            Session Type\n          </label>\n          <select\n            value={formData.sessionType}\n            onChange={(e) => setFormData({ ...formData, sessionType: e.target.value })}\n            className=\"w-full px-4 py-3 bg-gray-50 border border-gray-300 rounded-lg text-gray-900 focus:ring-2 focus:ring-amber-500 focus:border-transparent\"\n          >\n            <option value=\"In-Person\">In-Person</option>\n            <option value=\"Online\">Online</option>\n            <option value=\"Group\">Group</option>\n          </select>\n        </div>\n\n        <div className=\"grid grid-cols-2 gap-4\">\n          <div>\n            <label className=\"block text-gray-700 text-sm font-medium mb-2\">\n              Date\n            </label>\n            <input\n              type=\"date\"\n              value={formData.date}\n              onChange={(e) => setFormData({ ...formData, date: e.target.value })}\n              className=\"w-full px-4 py-3 bg-gray-50 border border-gray-300 rounded-lg text-gray-900 focus:ring-2 focus:ring-amber-500 focus:border-transparent\"\n              required\n            />\n          </div>\n          <div>\n            <label className=\"block text-gray-700 text-sm font-medium mb-2\">\n              Time\n            </label>\n            <input\n              type=\"time\"\n              value={formData.time}\n              onChange={(e) => setFormData({ ...formData, time: e.target.value })}\n              className=\"w-full px-4 py-3 bg-gray-50 border border-gray-300 rounded-lg text-gray-900 focus:ring-2 focus:ring-amber-500 focus:border-transparent\"\n              required\n            />\n          </div>\n        </div>\n\n        <div>\n          <label className=\"block text-gray-700 text-sm font-medium mb-2\">\n            Quote (£)\n          </label>\n          <input\n            type=\"number\"\n            value={formData.quote}\n            onChange={(e) => setFormData({ ...formData, quote: e.target.value })}\n            className=\"w-full px-4 py-3 bg-gray-50 border border-gray-300 rounded-lg text-gray-900 placeholder-gray-500 focus:ring-2 focus:ring-amber-500 focus:border-transparent\"\n            placeholder=\"Enter quote amount\"\n            min=\"0\"\n            step=\"0.01\"\n            required\n          />\n        </div>\n\n        <div className=\"space-y-4\">\n          <div className=\"flex justify-between items-center\">\n            <span className=\"text-gray-700 font-medium\">Paid</span>\n            <button\n              type=\"button\"\n              onClick={() => setFormData({ ...formData, paid: !formData.paid })}\n              className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${\n                formData.paid ? 'bg-green-500' : 'bg-gray-300'\n              }`}\n            >\n              <span\n                className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${\n                  formData.paid ? 'translate-x-6' : 'translate-x-1'\n                }`}\n              />\n            </button>\n          </div>\n\n          <div className=\"flex justify-between items-center\">\n            <span className=\"text-gray-700 font-medium\">Behaviour Plan Sent</span>\n            <button\n              type=\"button\"\n              onClick={() => setFormData({ ...formData, behaviourPlanSent: !formData.behaviourPlanSent })}\n              className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${\n                formData.behaviourPlanSent ? 'bg-green-500' : 'bg-gray-300'\n              }`}\n            >\n              <span\n                className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${\n                  formData.behaviourPlanSent ? 'translate-x-6' : 'translate-x-1'\n                }`}\n              />\n            </button>\n          </div>\n        </div>\n\n        <div>\n          <label className=\"block text-gray-700 text-sm font-medium mb-2\">\n            Notes (Optional)\n          </label>\n          <textarea\n            value={formData.notes}\n            onChange={(e) => setFormData({ ...formData, notes: e.target.value })}\n            className=\"w-full px-4 py-3 bg-gray-50 border border-gray-300 rounded-lg text-gray-900 placeholder-gray-500 focus:ring-2 focus:ring-amber-500 focus:border-transparent\"\n            placeholder=\"Add any notes about the session\"\n            rows={3}\n          />\n        </div>\n\n        <button\n          type=\"submit\"\n          className=\"w-full bg-amber-800 text-white py-3 px-6 rounded-lg font-medium hover:bg-amber-700 transition-colors\"\n        >\n          Update Session\n        </button>\n      </form>\n    </SlideUpModal>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AACA;AACA;AANA;;;;;;AAce,SAAS,iBAAiB,EAAE,OAAO,EAAE,MAAM,EAAE,OAAO,EAAyB;IAC1F,MAAM,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,6HAAA,CAAA,SAAM,AAAD;IAC1B,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,WAAW;QACX,SAAS;QACT,aAAa;QACb,MAAM;QACN,MAAM;QACN,OAAO;QACP,MAAM;QACN,mBAAmB;QACnB,OAAO;IACT;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,SAAS;YACX,MAAM,cAAc,IAAI,KAAK,QAAQ,WAAW;YAChD,YAAY;gBACV,WAAW,QAAQ,SAAS;gBAC5B,SAAS,QAAQ,OAAO,IAAI;gBAC5B,aAAa,QAAQ,WAAW;gBAChC,MAAM,CAAA,GAAA,qJAAA,CAAA,SAAM,AAAD,EAAE,aAAa;gBAC1B,MAAM,CAAA,GAAA,qJAAA,CAAA,SAAM,AAAD,EAAE,aAAa;gBAC1B,OAAO,QAAQ,KAAK,CAAC,QAAQ;gBAC7B,MAAM,QAAQ,IAAI;gBAClB,mBAAmB,QAAQ,iBAAiB;gBAC5C,OAAO,QAAQ,KAAK,IAAI;YAC1B;QACF;IACF,GAAG;QAAC;KAAQ;IAEZ,MAAM,eAAe,CAAC;QACpB,EAAE,cAAc;QAChB,IAAI,CAAC,SAAS;QAEd,MAAM,cAAc,IAAI,KAAK,GAAG,SAAS,IAAI,CAAC,CAAC,EAAE,SAAS,IAAI,EAAE;QAEhE,MAAM,iBAA0B;YAC9B,GAAG,OAAO;YACV,WAAW,SAAS,SAAS;YAC7B,SAAS,SAAS,OAAO,IAAI;YAC7B,aAAa,SAAS,WAAW;YACjC;YACA,OAAO,WAAW,SAAS,KAAK;YAChC,MAAM,SAAS,IAAI;YACnB,mBAAmB,SAAS,iBAAiB;YAC7C,OAAO,SAAS,KAAK,IAAI;QAC3B;QAEA,SAAS;YAAE,MAAM;YAAkB,SAAS;QAAe;QAC3D;IACF;IAEA,IAAI,CAAC,SAAS,OAAO;IAErB,qBACE,8OAAC,4IAAA,CAAA,UAAY;QACX,QAAQ;QACR,SAAS;QACT,OAAM;kBAEN,cAAA,8OAAC;YAAK,UAAU;YAAc,WAAU;;8BACtC,8OAAC;;sCACC,8OAAC;4BAAM,WAAU;sCAA+C;;;;;;sCAGhE,8OAAC;4BACC,MAAK;4BACL,OAAO,SAAS,SAAS;4BACzB,UAAU,CAAC,IAAM,YAAY;oCAAE,GAAG,QAAQ;oCAAE,WAAW,EAAE,MAAM,CAAC,KAAK;gCAAC;4BACtE,WAAU;4BACV,aAAY;4BACZ,QAAQ;;;;;;;;;;;;8BAIZ,8OAAC;;sCACC,8OAAC;4BAAM,WAAU;sCAA+C;;;;;;sCAGhE,8OAAC;4BACC,MAAK;4BACL,OAAO,SAAS,OAAO;4BACvB,UAAU,CAAC,IAAM,YAAY;oCAAE,GAAG,QAAQ;oCAAE,SAAS,EAAE,MAAM,CAAC,KAAK;gCAAC;4BACpE,WAAU;4BACV,aAAY;;;;;;;;;;;;8BAIhB,8OAAC;;sCACC,8OAAC;4BAAM,WAAU;sCAA+C;;;;;;sCAGhE,8OAAC;4BACC,OAAO,SAAS,WAAW;4BAC3B,UAAU,CAAC,IAAM,YAAY;oCAAE,GAAG,QAAQ;oCAAE,aAAa,EAAE,MAAM,CAAC,KAAK;gCAAC;4BACxE,WAAU;;8CAEV,8OAAC;oCAAO,OAAM;8CAAY;;;;;;8CAC1B,8OAAC;oCAAO,OAAM;8CAAS;;;;;;8CACvB,8OAAC;oCAAO,OAAM;8CAAQ;;;;;;;;;;;;;;;;;;8BAI1B,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;;8CACC,8OAAC;oCAAM,WAAU;8CAA+C;;;;;;8CAGhE,8OAAC;oCACC,MAAK;oCACL,OAAO,SAAS,IAAI;oCACpB,UAAU,CAAC,IAAM,YAAY;4CAAE,GAAG,QAAQ;4CAAE,MAAM,EAAE,MAAM,CAAC,KAAK;wCAAC;oCACjE,WAAU;oCACV,QAAQ;;;;;;;;;;;;sCAGZ,8OAAC;;8CACC,8OAAC;oCAAM,WAAU;8CAA+C;;;;;;8CAGhE,8OAAC;oCACC,MAAK;oCACL,OAAO,SAAS,IAAI;oCACpB,UAAU,CAAC,IAAM,YAAY;4CAAE,GAAG,QAAQ;4CAAE,MAAM,EAAE,MAAM,CAAC,KAAK;wCAAC;oCACjE,WAAU;oCACV,QAAQ;;;;;;;;;;;;;;;;;;8BAKd,8OAAC;;sCACC,8OAAC;4BAAM,WAAU;sCAA+C;;;;;;sCAGhE,8OAAC;4BACC,MAAK;4BACL,OAAO,SAAS,KAAK;4BACrB,UAAU,CAAC,IAAM,YAAY;oCAAE,GAAG,QAAQ;oCAAE,OAAO,EAAE,MAAM,CAAC,KAAK;gCAAC;4BAClE,WAAU;4BACV,aAAY;4BACZ,KAAI;4BACJ,MAAK;4BACL,QAAQ;;;;;;;;;;;;8BAIZ,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAK,WAAU;8CAA4B;;;;;;8CAC5C,8OAAC;oCACC,MAAK;oCACL,SAAS,IAAM,YAAY;4CAAE,GAAG,QAAQ;4CAAE,MAAM,CAAC,SAAS,IAAI;wCAAC;oCAC/D,WAAW,CAAC,0EAA0E,EACpF,SAAS,IAAI,GAAG,iBAAiB,eACjC;8CAEF,cAAA,8OAAC;wCACC,WAAW,CAAC,0EAA0E,EACpF,SAAS,IAAI,GAAG,kBAAkB,iBAClC;;;;;;;;;;;;;;;;;sCAKR,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAK,WAAU;8CAA4B;;;;;;8CAC5C,8OAAC;oCACC,MAAK;oCACL,SAAS,IAAM,YAAY;4CAAE,GAAG,QAAQ;4CAAE,mBAAmB,CAAC,SAAS,iBAAiB;wCAAC;oCACzF,WAAW,CAAC,0EAA0E,EACpF,SAAS,iBAAiB,GAAG,iBAAiB,eAC9C;8CAEF,cAAA,8OAAC;wCACC,WAAW,CAAC,0EAA0E,EACpF,SAAS,iBAAiB,GAAG,kBAAkB,iBAC/C;;;;;;;;;;;;;;;;;;;;;;;8BAMV,8OAAC;;sCACC,8OAAC;4BAAM,WAAU;sCAA+C;;;;;;sCAGhE,8OAAC;4BACC,OAAO,SAAS,KAAK;4BACrB,UAAU,CAAC,IAAM,YAAY;oCAAE,GAAG,QAAQ;oCAAE,OAAO,EAAE,MAAM,CAAC,KAAK;gCAAC;4BAClE,WAAU;4BACV,aAAY;4BACZ,MAAM;;;;;;;;;;;;8BAIV,8OAAC;oBACC,MAAK;oBACL,WAAU;8BACX;;;;;;;;;;;;;;;;;AAMT", "debugId": null}}, {"offset": {"line": 976, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/RMR%20App%20Version%202/raising-my-rescue/src/components/modals/EditClientModal.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { Client } from '@/types';\nimport { useApp } from '@/context/AppContext';\nimport SlideUpModal from './SlideUpModal';\n\ninterface EditClientModalProps {\n  client: Client | null;\n  isOpen: boolean;\n  onClose: () => void;\n}\n\nexport default function EditClientModal({ client, isOpen, onClose }: EditClientModalProps) {\n  const { dispatch } = useApp();\n  const [formData, setFormData] = useState({\n    ownerName: '',\n    email: '',\n    phone: '',\n    dogName: '',\n    dogBreed: '',\n    dogAge: '',\n    active: true,\n    notes: ''\n  });\n\n  useEffect(() => {\n    if (client) {\n      setFormData({\n        ownerName: client.ownerName,\n        email: client.email || '',\n        phone: client.phone || '',\n        dogName: client.dogName || '',\n        dogBreed: client.dogBreed || '',\n        dogAge: client.dogAge || '',\n        active: client.active,\n        notes: client.notes || ''\n      });\n    }\n  }, [client]);\n\n  const handleSubmit = (e: React.FormEvent) => {\n    e.preventDefault();\n    if (!client) return;\n\n    const updatedClient: Client = {\n      ...client,\n      ownerName: formData.ownerName,\n      email: formData.email || undefined,\n      phone: formData.phone || undefined,\n      dogName: formData.dogName || undefined,\n      dogBreed: formData.dogBreed || undefined,\n      dogAge: formData.dogAge || undefined,\n      active: formData.active,\n      notes: formData.notes || undefined\n    };\n\n    dispatch({ type: 'UPDATE_CLIENT', payload: updatedClient });\n    onClose();\n  };\n\n  if (!client) return null;\n\n  return (\n    <SlideUpModal\n      isOpen={isOpen}\n      onClose={onClose}\n      title=\"Edit Client\"\n    >\n      <form onSubmit={handleSubmit} className=\"space-y-6\">\n        <div>\n          <label className=\"block text-gray-700 text-sm font-medium mb-2\">\n            Owner Name\n          </label>\n          <input\n            type=\"text\"\n            value={formData.ownerName}\n            onChange={(e) => setFormData({ ...formData, ownerName: e.target.value })}\n            className=\"w-full px-4 py-3 bg-gray-50 border border-gray-300 rounded-lg text-gray-900 placeholder-gray-500 focus:ring-2 focus:ring-amber-500 focus:border-transparent\"\n            placeholder=\"Enter owner name\"\n            required\n          />\n        </div>\n\n        <div>\n          <label className=\"block text-gray-700 text-sm font-medium mb-2\">\n            Email (Optional)\n          </label>\n          <input\n            type=\"email\"\n            value={formData.email}\n            onChange={(e) => setFormData({ ...formData, email: e.target.value })}\n            className=\"w-full px-4 py-3 bg-gray-50 border border-gray-300 rounded-lg text-gray-900 placeholder-gray-500 focus:ring-2 focus:ring-amber-500 focus:border-transparent\"\n            placeholder=\"Enter email address\"\n          />\n        </div>\n\n        <div>\n          <label className=\"block text-gray-700 text-sm font-medium mb-2\">\n            Phone (Optional)\n          </label>\n          <input\n            type=\"tel\"\n            value={formData.phone}\n            onChange={(e) => setFormData({ ...formData, phone: e.target.value })}\n            className=\"w-full px-4 py-3 bg-gray-50 border border-gray-300 rounded-lg text-gray-900 placeholder-gray-500 focus:ring-2 focus:ring-amber-500 focus:border-transparent\"\n            placeholder=\"Enter phone number\"\n          />\n        </div>\n\n        <div>\n          <label className=\"block text-gray-700 text-sm font-medium mb-2\">\n            Dog Name (Optional)\n          </label>\n          <input\n            type=\"text\"\n            value={formData.dogName}\n            onChange={(e) => setFormData({ ...formData, dogName: e.target.value })}\n            className=\"w-full px-4 py-3 bg-gray-50 border border-gray-300 rounded-lg text-gray-900 placeholder-gray-500 focus:ring-2 focus:ring-amber-500 focus:border-transparent\"\n            placeholder=\"Enter dog name\"\n          />\n        </div>\n\n        <div className=\"grid grid-cols-2 gap-4\">\n          <div>\n            <label className=\"block text-gray-700 text-sm font-medium mb-2\">\n              Dog Breed (Optional)\n            </label>\n            <input\n              type=\"text\"\n              value={formData.dogBreed}\n              onChange={(e) => setFormData({ ...formData, dogBreed: e.target.value })}\n              className=\"w-full px-4 py-3 bg-gray-50 border border-gray-300 rounded-lg text-gray-900 placeholder-gray-500 focus:ring-2 focus:ring-amber-500 focus:border-transparent\"\n              placeholder=\"Enter breed\"\n            />\n          </div>\n          <div>\n            <label className=\"block text-gray-700 text-sm font-medium mb-2\">\n              Dog Age (Optional)\n            </label>\n            <input\n              type=\"text\"\n              value={formData.dogAge}\n              onChange={(e) => setFormData({ ...formData, dogAge: e.target.value })}\n              className=\"w-full px-4 py-3 bg-gray-50 border border-gray-300 rounded-lg text-gray-900 placeholder-gray-500 focus:ring-2 focus:ring-amber-500 focus:border-transparent\"\n              placeholder=\"Enter age\"\n            />\n          </div>\n        </div>\n\n        <div className=\"flex justify-between items-center\">\n          <span className=\"text-gray-700 font-medium\">Active Client</span>\n          <button\n            type=\"button\"\n            onClick={() => setFormData({ ...formData, active: !formData.active })}\n            className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${\n              formData.active ? 'bg-green-500' : 'bg-gray-300'\n            }`}\n          >\n            <span\n              className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${\n                formData.active ? 'translate-x-6' : 'translate-x-1'\n              }`}\n            />\n          </button>\n        </div>\n\n        <div>\n          <label className=\"block text-gray-700 text-sm font-medium mb-2\">\n            Notes (Optional)\n          </label>\n          <textarea\n            value={formData.notes}\n            onChange={(e) => setFormData({ ...formData, notes: e.target.value })}\n            className=\"w-full px-4 py-3 bg-gray-50 border border-gray-300 rounded-lg text-gray-900 placeholder-gray-500 focus:ring-2 focus:ring-amber-500 focus:border-transparent\"\n            placeholder=\"Add any notes about the client\"\n            rows={3}\n          />\n        </div>\n\n        <button\n          type=\"submit\"\n          className=\"w-full bg-amber-800 text-white py-3 px-6 rounded-lg font-medium hover:bg-amber-700 transition-colors\"\n        >\n          Update Client\n        </button>\n      </form>\n    </SlideUpModal>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AACA;AALA;;;;;AAae,SAAS,gBAAgB,EAAE,MAAM,EAAE,MAAM,EAAE,OAAO,EAAwB;IACvF,MAAM,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,6HAAA,CAAA,SAAM,AAAD;IAC1B,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,WAAW;QACX,OAAO;QACP,OAAO;QACP,SAAS;QACT,UAAU;QACV,QAAQ;QACR,QAAQ;QACR,OAAO;IACT;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,QAAQ;YACV,YAAY;gBACV,WAAW,OAAO,SAAS;gBAC3B,OAAO,OAAO,KAAK,IAAI;gBACvB,OAAO,OAAO,KAAK,IAAI;gBACvB,SAAS,OAAO,OAAO,IAAI;gBAC3B,UAAU,OAAO,QAAQ,IAAI;gBAC7B,QAAQ,OAAO,MAAM,IAAI;gBACzB,QAAQ,OAAO,MAAM;gBACrB,OAAO,OAAO,KAAK,IAAI;YACzB;QACF;IACF,GAAG;QAAC;KAAO;IAEX,MAAM,eAAe,CAAC;QACpB,EAAE,cAAc;QAChB,IAAI,CAAC,QAAQ;QAEb,MAAM,gBAAwB;YAC5B,GAAG,MAAM;YACT,WAAW,SAAS,SAAS;YAC7B,OAAO,SAAS,KAAK,IAAI;YACzB,OAAO,SAAS,KAAK,IAAI;YACzB,SAAS,SAAS,OAAO,IAAI;YAC7B,UAAU,SAAS,QAAQ,IAAI;YAC/B,QAAQ,SAAS,MAAM,IAAI;YAC3B,QAAQ,SAAS,MAAM;YACvB,OAAO,SAAS,KAAK,IAAI;QAC3B;QAEA,SAAS;YAAE,MAAM;YAAiB,SAAS;QAAc;QACzD;IACF;IAEA,IAAI,CAAC,QAAQ,OAAO;IAEpB,qBACE,8OAAC,4IAAA,CAAA,UAAY;QACX,QAAQ;QACR,SAAS;QACT,OAAM;kBAEN,cAAA,8OAAC;YAAK,UAAU;YAAc,WAAU;;8BACtC,8OAAC;;sCACC,8OAAC;4BAAM,WAAU;sCAA+C;;;;;;sCAGhE,8OAAC;4BACC,MAAK;4BACL,OAAO,SAAS,SAAS;4BACzB,UAAU,CAAC,IAAM,YAAY;oCAAE,GAAG,QAAQ;oCAAE,WAAW,EAAE,MAAM,CAAC,KAAK;gCAAC;4BACtE,WAAU;4BACV,aAAY;4BACZ,QAAQ;;;;;;;;;;;;8BAIZ,8OAAC;;sCACC,8OAAC;4BAAM,WAAU;sCAA+C;;;;;;sCAGhE,8OAAC;4BACC,MAAK;4BACL,OAAO,SAAS,KAAK;4BACrB,UAAU,CAAC,IAAM,YAAY;oCAAE,GAAG,QAAQ;oCAAE,OAAO,EAAE,MAAM,CAAC,KAAK;gCAAC;4BAClE,WAAU;4BACV,aAAY;;;;;;;;;;;;8BAIhB,8OAAC;;sCACC,8OAAC;4BAAM,WAAU;sCAA+C;;;;;;sCAGhE,8OAAC;4BACC,MAAK;4BACL,OAAO,SAAS,KAAK;4BACrB,UAAU,CAAC,IAAM,YAAY;oCAAE,GAAG,QAAQ;oCAAE,OAAO,EAAE,MAAM,CAAC,KAAK;gCAAC;4BAClE,WAAU;4BACV,aAAY;;;;;;;;;;;;8BAIhB,8OAAC;;sCACC,8OAAC;4BAAM,WAAU;sCAA+C;;;;;;sCAGhE,8OAAC;4BACC,MAAK;4BACL,OAAO,SAAS,OAAO;4BACvB,UAAU,CAAC,IAAM,YAAY;oCAAE,GAAG,QAAQ;oCAAE,SAAS,EAAE,MAAM,CAAC,KAAK;gCAAC;4BACpE,WAAU;4BACV,aAAY;;;;;;;;;;;;8BAIhB,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;;8CACC,8OAAC;oCAAM,WAAU;8CAA+C;;;;;;8CAGhE,8OAAC;oCACC,MAAK;oCACL,OAAO,SAAS,QAAQ;oCACxB,UAAU,CAAC,IAAM,YAAY;4CAAE,GAAG,QAAQ;4CAAE,UAAU,EAAE,MAAM,CAAC,KAAK;wCAAC;oCACrE,WAAU;oCACV,aAAY;;;;;;;;;;;;sCAGhB,8OAAC;;8CACC,8OAAC;oCAAM,WAAU;8CAA+C;;;;;;8CAGhE,8OAAC;oCACC,MAAK;oCACL,OAAO,SAAS,MAAM;oCACtB,UAAU,CAAC,IAAM,YAAY;4CAAE,GAAG,QAAQ;4CAAE,QAAQ,EAAE,MAAM,CAAC,KAAK;wCAAC;oCACnE,WAAU;oCACV,aAAY;;;;;;;;;;;;;;;;;;8BAKlB,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAK,WAAU;sCAA4B;;;;;;sCAC5C,8OAAC;4BACC,MAAK;4BACL,SAAS,IAAM,YAAY;oCAAE,GAAG,QAAQ;oCAAE,QAAQ,CAAC,SAAS,MAAM;gCAAC;4BACnE,WAAW,CAAC,0EAA0E,EACpF,SAAS,MAAM,GAAG,iBAAiB,eACnC;sCAEF,cAAA,8OAAC;gCACC,WAAW,CAAC,0EAA0E,EACpF,SAAS,MAAM,GAAG,kBAAkB,iBACpC;;;;;;;;;;;;;;;;;8BAKR,8OAAC;;sCACC,8OAAC;4BAAM,WAAU;sCAA+C;;;;;;sCAGhE,8OAAC;4BACC,OAAO,SAAS,KAAK;4BACrB,UAAU,CAAC,IAAM,YAAY;oCAAE,GAAG,QAAQ;oCAAE,OAAO,EAAE,MAAM,CAAC,KAAK;gCAAC;4BAClE,WAAU;4BACV,aAAY;4BACZ,MAAM;;;;;;;;;;;;8BAIV,8OAAC;oBACC,MAAK;oBACL,WAAU;8BACX;;;;;;;;;;;;;;;;;AAMT", "debugId": null}}, {"offset": {"line": 1328, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/RMR%20App%20Version%202/raising-my-rescue/src/components/AddModal.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { X } from 'lucide-react';\nimport { useModal } from '@/context/ModalContext';\nimport { useApp } from '@/context/AppContext';\n\ninterface AddModalProps {\n  isOpen: boolean;\n  onClose: () => void;\n  type: 'session' | 'client';\n}\n\nexport default function AddModal({ isOpen, onClose, type }: AddModalProps) {\n  const [isVisible, setIsVisible] = useState(false);\n  const [isAnimating, setIsAnimating] = useState(false);\n  const { registerModal, unregisterModal } = useModal();\n  const [modalId] = useState(() => `add-modal-${Math.random().toString(36).substr(2, 9)}`);\n\n  useEffect(() => {\n    if (isOpen) {\n      setIsVisible(true);\n      registerModal(modalId);\n      // Small delay to trigger animation\n      setTimeout(() => setIsAnimating(true), 10);\n    } else {\n      setIsAnimating(false);\n      // Wait for animation to complete before hiding\n      setTimeout(() => {\n        setIsVisible(false);\n        unregisterModal(modalId);\n      }, 300);\n    }\n  }, [isOpen, registerModal, unregisterModal, modalId]);\n\n  const handleClose = () => {\n    setIsAnimating(false);\n    setTimeout(() => {\n      setIsVisible(false);\n      onClose();\n    }, 300);\n  };\n\n  const handleBackdropClick = (e: React.MouseEvent) => {\n    if (e.target === e.currentTarget) {\n      handleClose();\n    }\n  };\n\n  if (!isVisible) return null;\n\n  return (\n    <div \n      className={`fixed inset-0 z-50 transition-all duration-300 ${\n        isAnimating ? 'bg-black/50' : 'bg-black/0'\n      }`}\n      onClick={handleBackdropClick}\n    >\n      <div\n        className={`fixed bottom-0 left-0 right-0 bg-white rounded-t-3xl shadow-2xl transition-transform duration-300 ease-out ${\n          isAnimating ? 'translate-y-0' : 'translate-y-full'\n        }`}\n        style={{ maxHeight: '90vh' }}\n      >\n        {/* Header */}\n        <div className=\"flex items-center justify-between p-6 border-b border-gray-200\">\n          <h2 className=\"text-xl font-semibold\">\n            {type === 'session' ? 'Add Session' : 'Add Client'}\n          </h2>\n          <button\n            onClick={handleClose}\n            className=\"p-2 hover:bg-gray-100 rounded-full transition-colors\"\n          >\n            <X size={24} />\n          </button>\n        </div>\n\n        {/* Content */}\n        <div className=\"p-6 overflow-y-auto\" style={{ maxHeight: 'calc(90vh - 80px)' }}>\n          {type === 'session' ? (\n            <SessionForm onSubmit={handleClose} />\n          ) : (\n            <ClientForm onSubmit={handleClose} />\n          )}\n        </div>\n      </div>\n    </div>\n  );\n}\n\nfunction SessionForm({ onSubmit }: { onSubmit: () => void }) {\n  const { state } = useApp();\n  const [formData, setFormData] = useState({\n    clientId: '',\n    sessionType: 'In-Person',\n    date: '',\n    time: '',\n    notes: ''\n  });\n\n  const handleSubmit = (e: React.FormEvent) => {\n    e.preventDefault();\n    // TODO: Implement session creation\n    console.log('Creating session:', formData);\n    onSubmit();\n  };\n\n  return (\n    <form onSubmit={handleSubmit} className=\"space-y-6\">\n      <div>\n        <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n          Client\n        </label>\n        <select\n          value={formData.clientId}\n          onChange={(e) => setFormData({ ...formData, clientId: e.target.value })}\n          className=\"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-transparent\"\n          required\n        >\n          <option value=\"\">Select a client</option>\n          {state.clients.map(client => (\n            <option key={client.id} value={client.id}>\n              {client.ownerName}{client.dogName ? ` w/ ${client.dogName}` : ''}\n            </option>\n          ))}\n        </select>\n      </div>\n\n      <div>\n        <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n          Session Type\n        </label>\n        <select\n          value={formData.sessionType}\n          onChange={(e) => setFormData({ ...formData, sessionType: e.target.value })}\n          className=\"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-transparent\"\n        >\n          <option value=\"In-Person\">In-Person</option>\n          <option value=\"Virtual\">Virtual</option>\n          <option value=\"Phone\">Phone</option>\n        </select>\n      </div>\n\n      <div className=\"grid grid-cols-2 gap-4\">\n        <div>\n          <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n            Date\n          </label>\n          <input\n            type=\"date\"\n            value={formData.date}\n            onChange={(e) => setFormData({ ...formData, date: e.target.value })}\n            className=\"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-transparent\"\n            required\n          />\n        </div>\n\n        <div>\n          <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n            Time\n          </label>\n          <input\n            type=\"time\"\n            value={formData.time}\n            onChange={(e) => setFormData({ ...formData, time: e.target.value })}\n            className=\"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-transparent\"\n            required\n          />\n        </div>\n      </div>\n\n      <div>\n        <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n          Notes (Optional)\n        </label>\n        <textarea\n          value={formData.notes}\n          onChange={(e) => setFormData({ ...formData, notes: e.target.value })}\n          className=\"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-transparent\"\n          placeholder=\"Add any notes about the session\"\n          rows={3}\n        />\n      </div>\n\n      <button\n        type=\"submit\"\n        className=\"w-full bg-amber-800 text-white py-3 px-6 rounded-lg font-medium hover:bg-amber-700 transition-colors\"\n      >\n        Create Session\n      </button>\n    </form>\n  );\n}\n\nfunction ClientForm({ onSubmit }: { onSubmit: () => void }) {\n  const [formData, setFormData] = useState({\n    ownerName: '',\n    email: '',\n    phone: '',\n    dogName: '',\n    dogBreed: '',\n    dogAge: '',\n    notes: ''\n  });\n\n  const handleSubmit = (e: React.FormEvent) => {\n    e.preventDefault();\n    // TODO: Implement client creation\n    console.log('Creating client:', formData);\n    onSubmit();\n  };\n\n  return (\n    <form onSubmit={handleSubmit} className=\"space-y-6\">\n      <div>\n        <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n          Owner Name\n        </label>\n        <input\n          type=\"text\"\n          value={formData.ownerName}\n          onChange={(e) => setFormData({ ...formData, ownerName: e.target.value })}\n          className=\"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-transparent\"\n          placeholder=\"Enter owner name\"\n          required\n        />\n      </div>\n\n      <div className=\"grid grid-cols-1 gap-4\">\n        <div>\n          <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n            Email\n          </label>\n          <input\n            type=\"email\"\n            value={formData.email}\n            onChange={(e) => setFormData({ ...formData, email: e.target.value })}\n            className=\"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-transparent\"\n            placeholder=\"Enter email address\"\n            required\n          />\n        </div>\n\n        <div>\n          <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n            Phone\n          </label>\n          <input\n            type=\"tel\"\n            value={formData.phone}\n            onChange={(e) => setFormData({ ...formData, phone: e.target.value })}\n            className=\"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-transparent\"\n            placeholder=\"Enter phone number\"\n          />\n        </div>\n      </div>\n\n      <div>\n        <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n          Dog Name\n        </label>\n        <input\n          type=\"text\"\n          value={formData.dogName}\n          onChange={(e) => setFormData({ ...formData, dogName: e.target.value })}\n          className=\"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-transparent\"\n          placeholder=\"Enter dog name\"\n          required\n        />\n      </div>\n\n      <div className=\"grid grid-cols-2 gap-4\">\n        <div>\n          <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n            Breed\n          </label>\n          <input\n            type=\"text\"\n            value={formData.dogBreed}\n            onChange={(e) => setFormData({ ...formData, dogBreed: e.target.value })}\n            className=\"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-transparent\"\n            placeholder=\"Enter dog breed\"\n          />\n        </div>\n\n        <div>\n          <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n            Age\n          </label>\n          <input\n            type=\"text\"\n            value={formData.dogAge}\n            onChange={(e) => setFormData({ ...formData, dogAge: e.target.value })}\n            className=\"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-transparent\"\n            placeholder=\"e.g., 2 years\"\n          />\n        </div>\n      </div>\n\n      <div>\n        <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n          Notes (Optional)\n        </label>\n        <textarea\n          value={formData.notes}\n          onChange={(e) => setFormData({ ...formData, notes: e.target.value })}\n          className=\"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-transparent\"\n          placeholder=\"Add any notes about the client or dog\"\n          rows={3}\n        />\n      </div>\n\n      <button\n        type=\"submit\"\n        className=\"w-full bg-amber-800 text-white py-3 px-6 rounded-lg font-medium hover:bg-amber-700 transition-colors\"\n      >\n        Create Client\n      </button>\n    </form>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AALA;;;;;;AAae,SAAS,SAAS,EAAE,MAAM,EAAE,OAAO,EAAE,IAAI,EAAiB;IACvE,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,EAAE,aAAa,EAAE,eAAe,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,WAAQ,AAAD;IAClD,MAAM,CAAC,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,IAAM,CAAC,UAAU,EAAE,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG,IAAI;IAEvF,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,QAAQ;YACV,aAAa;YACb,cAAc;YACd,mCAAmC;YACnC,WAAW,IAAM,eAAe,OAAO;QACzC,OAAO;YACL,eAAe;YACf,+CAA+C;YAC/C,WAAW;gBACT,aAAa;gBACb,gBAAgB;YAClB,GAAG;QACL;IACF,GAAG;QAAC;QAAQ;QAAe;QAAiB;KAAQ;IAEpD,MAAM,cAAc;QAClB,eAAe;QACf,WAAW;YACT,aAAa;YACb;QACF,GAAG;IACL;IAEA,MAAM,sBAAsB,CAAC;QAC3B,IAAI,EAAE,MAAM,KAAK,EAAE,aAAa,EAAE;YAChC;QACF;IACF;IAEA,IAAI,CAAC,WAAW,OAAO;IAEvB,qBACE,8OAAC;QACC,WAAW,CAAC,+CAA+C,EACzD,cAAc,gBAAgB,cAC9B;QACF,SAAS;kBAET,cAAA,8OAAC;YACC,WAAW,CAAC,2GAA2G,EACrH,cAAc,kBAAkB,oBAChC;YACF,OAAO;gBAAE,WAAW;YAAO;;8BAG3B,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCACX,SAAS,YAAY,gBAAgB;;;;;;sCAExC,8OAAC;4BACC,SAAS;4BACT,WAAU;sCAEV,cAAA,8OAAC,4LAAA,CAAA,IAAC;gCAAC,MAAM;;;;;;;;;;;;;;;;;8BAKb,8OAAC;oBAAI,WAAU;oBAAsB,OAAO;wBAAE,WAAW;oBAAoB;8BAC1E,SAAS,0BACR,8OAAC;wBAAY,UAAU;;;;;6CAEvB,8OAAC;wBAAW,UAAU;;;;;;;;;;;;;;;;;;;;;;AAMlC;AAEA,SAAS,YAAY,EAAE,QAAQ,EAA4B;IACzD,MAAM,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,6HAAA,CAAA,SAAM,AAAD;IACvB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,UAAU;QACV,aAAa;QACb,MAAM;QACN,MAAM;QACN,OAAO;IACT;IAEA,MAAM,eAAe,CAAC;QACpB,EAAE,cAAc;QAChB,mCAAmC;QACnC,QAAQ,GAAG,CAAC,qBAAqB;QACjC;IACF;IAEA,qBACE,8OAAC;QAAK,UAAU;QAAc,WAAU;;0BACtC,8OAAC;;kCACC,8OAAC;wBAAM,WAAU;kCAA+C;;;;;;kCAGhE,8OAAC;wBACC,OAAO,SAAS,QAAQ;wBACxB,UAAU,CAAC,IAAM,YAAY;gCAAE,GAAG,QAAQ;gCAAE,UAAU,EAAE,MAAM,CAAC,KAAK;4BAAC;wBACrE,WAAU;wBACV,QAAQ;;0CAER,8OAAC;gCAAO,OAAM;0CAAG;;;;;;4BAChB,MAAM,OAAO,CAAC,GAAG,CAAC,CAAA,uBACjB,8OAAC;oCAAuB,OAAO,OAAO,EAAE;;wCACrC,OAAO,SAAS;wCAAE,OAAO,OAAO,GAAG,CAAC,IAAI,EAAE,OAAO,OAAO,EAAE,GAAG;;mCADnD,OAAO,EAAE;;;;;;;;;;;;;;;;;0BAO5B,8OAAC;;kCACC,8OAAC;wBAAM,WAAU;kCAA+C;;;;;;kCAGhE,8OAAC;wBACC,OAAO,SAAS,WAAW;wBAC3B,UAAU,CAAC,IAAM,YAAY;gCAAE,GAAG,QAAQ;gCAAE,aAAa,EAAE,MAAM,CAAC,KAAK;4BAAC;wBACxE,WAAU;;0CAEV,8OAAC;gCAAO,OAAM;0CAAY;;;;;;0CAC1B,8OAAC;gCAAO,OAAM;0CAAU;;;;;;0CACxB,8OAAC;gCAAO,OAAM;0CAAQ;;;;;;;;;;;;;;;;;;0BAI1B,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;;0CACC,8OAAC;gCAAM,WAAU;0CAA+C;;;;;;0CAGhE,8OAAC;gCACC,MAAK;gCACL,OAAO,SAAS,IAAI;gCACpB,UAAU,CAAC,IAAM,YAAY;wCAAE,GAAG,QAAQ;wCAAE,MAAM,EAAE,MAAM,CAAC,KAAK;oCAAC;gCACjE,WAAU;gCACV,QAAQ;;;;;;;;;;;;kCAIZ,8OAAC;;0CACC,8OAAC;gCAAM,WAAU;0CAA+C;;;;;;0CAGhE,8OAAC;gCACC,MAAK;gCACL,OAAO,SAAS,IAAI;gCACpB,UAAU,CAAC,IAAM,YAAY;wCAAE,GAAG,QAAQ;wCAAE,MAAM,EAAE,MAAM,CAAC,KAAK;oCAAC;gCACjE,WAAU;gCACV,QAAQ;;;;;;;;;;;;;;;;;;0BAKd,8OAAC;;kCACC,8OAAC;wBAAM,WAAU;kCAA+C;;;;;;kCAGhE,8OAAC;wBACC,OAAO,SAAS,KAAK;wBACrB,UAAU,CAAC,IAAM,YAAY;gCAAE,GAAG,QAAQ;gCAAE,OAAO,EAAE,MAAM,CAAC,KAAK;4BAAC;wBAClE,WAAU;wBACV,aAAY;wBACZ,MAAM;;;;;;;;;;;;0BAIV,8OAAC;gBACC,MAAK;gBACL,WAAU;0BACX;;;;;;;;;;;;AAKP;AAEA,SAAS,WAAW,EAAE,QAAQ,EAA4B;IACxD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,WAAW;QACX,OAAO;QACP,OAAO;QACP,SAAS;QACT,UAAU;QACV,QAAQ;QACR,OAAO;IACT;IAEA,MAAM,eAAe,CAAC;QACpB,EAAE,cAAc;QAChB,kCAAkC;QAClC,QAAQ,GAAG,CAAC,oBAAoB;QAChC;IACF;IAEA,qBACE,8OAAC;QAAK,UAAU;QAAc,WAAU;;0BACtC,8OAAC;;kCACC,8OAAC;wBAAM,WAAU;kCAA+C;;;;;;kCAGhE,8OAAC;wBACC,MAAK;wBACL,OAAO,SAAS,SAAS;wBACzB,UAAU,CAAC,IAAM,YAAY;gCAAE,GAAG,QAAQ;gCAAE,WAAW,EAAE,MAAM,CAAC,KAAK;4BAAC;wBACtE,WAAU;wBACV,aAAY;wBACZ,QAAQ;;;;;;;;;;;;0BAIZ,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;;0CACC,8OAAC;gCAAM,WAAU;0CAA+C;;;;;;0CAGhE,8OAAC;gCACC,MAAK;gCACL,OAAO,SAAS,KAAK;gCACrB,UAAU,CAAC,IAAM,YAAY;wCAAE,GAAG,QAAQ;wCAAE,OAAO,EAAE,MAAM,CAAC,KAAK;oCAAC;gCAClE,WAAU;gCACV,aAAY;gCACZ,QAAQ;;;;;;;;;;;;kCAIZ,8OAAC;;0CACC,8OAAC;gCAAM,WAAU;0CAA+C;;;;;;0CAGhE,8OAAC;gCACC,MAAK;gCACL,OAAO,SAAS,KAAK;gCACrB,UAAU,CAAC,IAAM,YAAY;wCAAE,GAAG,QAAQ;wCAAE,OAAO,EAAE,MAAM,CAAC,KAAK;oCAAC;gCAClE,WAAU;gCACV,aAAY;;;;;;;;;;;;;;;;;;0BAKlB,8OAAC;;kCACC,8OAAC;wBAAM,WAAU;kCAA+C;;;;;;kCAGhE,8OAAC;wBACC,MAAK;wBACL,OAAO,SAAS,OAAO;wBACvB,UAAU,CAAC,IAAM,YAAY;gCAAE,GAAG,QAAQ;gCAAE,SAAS,EAAE,MAAM,CAAC,KAAK;4BAAC;wBACpE,WAAU;wBACV,aAAY;wBACZ,QAAQ;;;;;;;;;;;;0BAIZ,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;;0CACC,8OAAC;gCAAM,WAAU;0CAA+C;;;;;;0CAGhE,8OAAC;gCACC,MAAK;gCACL,OAAO,SAAS,QAAQ;gCACxB,UAAU,CAAC,IAAM,YAAY;wCAAE,GAAG,QAAQ;wCAAE,UAAU,EAAE,MAAM,CAAC,KAAK;oCAAC;gCACrE,WAAU;gCACV,aAAY;;;;;;;;;;;;kCAIhB,8OAAC;;0CACC,8OAAC;gCAAM,WAAU;0CAA+C;;;;;;0CAGhE,8OAAC;gCACC,MAAK;gCACL,OAAO,SAAS,MAAM;gCACtB,UAAU,CAAC,IAAM,YAAY;wCAAE,GAAG,QAAQ;wCAAE,QAAQ,EAAE,MAAM,CAAC,KAAK;oCAAC;gCACnE,WAAU;gCACV,aAAY;;;;;;;;;;;;;;;;;;0BAKlB,8OAAC;;kCACC,8OAAC;wBAAM,WAAU;kCAA+C;;;;;;kCAGhE,8OAAC;wBACC,OAAO,SAAS,KAAK;wBACrB,UAAU,CAAC,IAAM,YAAY;gCAAE,GAAG,QAAQ;gCAAE,OAAO,EAAE,MAAM,CAAC,KAAK;4BAAC;wBAClE,WAAU;wBACV,aAAY;wBACZ,MAAM;;;;;;;;;;;;0BAIV,8OAAC;gBACC,MAAK;gBACL,WAAU;0BACX;;;;;;;;;;;;AAKP", "debugId": null}}, {"offset": {"line": 1967, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/RMR%20App%20Version%202/raising-my-rescue/src/app/calendar/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { useApp } from '@/context/AppContext';\nimport Header from '@/components/layout/Header';\nimport SessionModal from '@/components/modals/SessionModal';\nimport EditSessionModal from '@/components/modals/EditSessionModal';\nimport EditClientModal from '@/components/modals/EditClientModal';\nimport AddModal from '@/components/AddModal';\nimport { Session, Client } from '@/types';\nimport { format, startOfMonth, endOfMonth, eachDayOfInterval, isSameDay, addMonths, subMonths } from 'date-fns';\nimport { ChevronLeft, ChevronRight } from 'lucide-react';\n\nexport default function CalendarPage() {\n  const { state } = useApp();\n  const [currentDate, setCurrentDate] = useState(new Date());\n  const [selectedSession, setSelectedSession] = useState<Session | null>(null);\n  const [showAddModal, setShowAddModal] = useState(false);\n  const [addModalType, setAddModalType] = useState<'session' | 'client'>('session');\n  const [showEditSessionModal, setShowEditSessionModal] = useState(false);\n  const [showEditClientModal, setShowEditClientModal] = useState(false);\n  const [editingClient, setEditingClient] = useState<Client | null>(null);\n\n  const monthStart = startOfMonth(currentDate);\n  const monthEnd = endOfMonth(currentDate);\n  const daysInMonth = eachDayOfInterval({ start: monthStart, end: monthEnd });\n\n  const getSessionsForDay = (day: Date) => {\n    return state.sessions.filter(session =>\n      isSameDay(new Date(session.bookingDate), day)\n    );\n  };\n\n  const handlePreviousMonth = () => {\n    setCurrentDate(subMonths(currentDate, 1));\n  };\n\n  const handleNextMonth = () => {\n    setCurrentDate(addMonths(currentDate, 1));\n  };\n\n  const handleSessionClick = (session: Session) => {\n    setSelectedSession(session);\n  };\n\n  const handleCloseModal = () => {\n    setSelectedSession(null);\n  };\n\n  const handleAddSession = () => {\n    setAddModalType('session');\n    setShowAddModal(true);\n  };\n\n  const handleAddClient = () => {\n    setAddModalType('client');\n    setShowAddModal(true);\n  };\n\n  const handleCloseAddModal = () => {\n    setShowAddModal(false);\n  };\n\n  const handleEditSession = (session: Session) => {\n    setSelectedSession(session);\n    setShowEditSessionModal(true);\n  };\n\n  const handleEditClient = (session: Session) => {\n    // Find the client based on the session's owner name\n    const client = state.clients.find(c => c.ownerName === session.ownerName);\n    if (client) {\n      setEditingClient(client);\n      setShowEditClientModal(true);\n    }\n  };\n\n  const handleCloseEditSessionModal = () => {\n    setShowEditSessionModal(false);\n  };\n\n  const handleCloseEditClientModal = () => {\n    setShowEditClientModal(false);\n    setEditingClient(null);\n  };\n\n  const handleUpNextClick = () => {\n    if (firstSession) {\n      setSelectedSession(firstSession);\n    }\n  };\n\n  // Keyboard navigation for months\n  useEffect(() => {\n    const handleKeyDown = (event: KeyboardEvent) => {\n      if (event.key === 'ArrowLeft') {\n        event.preventDefault();\n        handlePreviousMonth();\n      } else if (event.key === 'ArrowRight') {\n        event.preventDefault();\n        handleNextMonth();\n      }\n    };\n\n    window.addEventListener('keydown', handleKeyDown);\n    return () => window.removeEventListener('keydown', handleKeyDown);\n  }, []);\n\n  // Focus the calendar container to enable keyboard navigation\n  useEffect(() => {\n    const calendarContainer = document.getElementById('calendar-container');\n    if (calendarContainer) {\n      calendarContainer.focus();\n    }\n  }, []);\n\n  // Get the first session for the bottom preview\n  const firstSession = state.sessions[0];\n\n  return (\n    <div\n      id=\"calendar-container\"\n      className=\"h-screen bg-amber-800 flex flex-col overflow-hidden outline-none\"\n      style={{ paddingTop: 'max(env(safe-area-inset-top), 20px)' }}\n      tabIndex={0}\n    >\n      <Header\n        title=\"Calendar\"\n        showAddButton\n        addButtonText=\"Add Session\"\n        onAddClick={handleAddSession}\n        showSearch\n        searchPlaceholder=\"Search\"\n      />\n\n      {/* Calendar Section - Flex-1 to take remaining space */}\n      <div className=\"bg-white flex flex-col flex-1 overflow-hidden\">\n        {/* Month Navigation */}\n        <div className=\"px-4 py-4 border-b border-gray-200 flex-shrink-0\">\n          <div className=\"flex items-center justify-between\">\n            <h2 className=\"text-lg font-semibold\">\n              {format(currentDate, 'MMM yyyy')}\n            </h2>\n            <div className=\"flex items-center gap-2\">\n              <button\n                onClick={handlePreviousMonth}\n                className=\"p-2 hover:bg-gray-100 rounded transition-colors\"\n              >\n                <ChevronLeft size={20} />\n              </button>\n              <button\n                onClick={handleNextMonth}\n                className=\"p-2 hover:bg-gray-100 rounded transition-colors\"\n              >\n                <ChevronRight size={20} />\n              </button>\n            </div>\n          </div>\n        </div>\n\n        {/* Calendar Grid - Fills remaining space */}\n        <div className=\"flex-1 px-4 py-2 flex flex-col min-h-0 overflow-hidden\">\n          <div className=\"grid grid-cols-7 gap-1 mb-2 flex-shrink-0\">\n            {['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'].map(day => (\n              <div key={day} className=\"text-center text-sm font-medium text-gray-500 py-2\">\n                {day}\n              </div>\n            ))}\n          </div>\n\n          <div className=\"grid grid-cols-7 gap-1 flex-1 min-h-0 auto-rows-fr\">\n            {daysInMonth.map(day => {\n              const sessions = getSessionsForDay(day);\n              const dayNumber = format(day, 'd');\n\n              return (\n                <div key={day.toISOString()} className=\"flex flex-col p-1 min-h-0 border-r border-b border-gray-100 last:border-r-0\">\n                  <div className=\"text-sm font-medium mb-1 flex-shrink-0\">{dayNumber}</div>\n                  <div className=\"space-y-1 flex-1 min-h-0 overflow-hidden\">\n                    {sessions.slice(0, 2).map(session => (\n                      <button\n                        key={session.id}\n                        onClick={() => handleSessionClick(session)}\n                        className=\"w-full bg-amber-800 text-white text-xs px-2 py-1 rounded text-left hover:bg-amber-700 transition-colors flex-shrink-0\"\n                      >\n                        {format(new Date(session.bookingDate), 'HH:mm')}...\n                      </button>\n                    ))}\n                    {sessions.length > 2 && (\n                      <div className=\"text-xs text-amber-800 font-medium flex-shrink-0\">\n                        +{sessions.length - 2} more\n                      </div>\n                    )}\n                  </div>\n                </div>\n              );\n            })}\n          </div>\n        </div>\n      </div>\n\n      {/* Up Next Section - Fixed at bottom */}\n      <button\n        onClick={handleUpNextClick}\n        disabled={!firstSession}\n        className=\"bg-amber-800 text-white px-4 py-4 flex-shrink-0 w-full text-left disabled:cursor-default\"\n        style={{ paddingBottom: 'max(env(safe-area-inset-bottom), 20px)' }}\n      >\n        {firstSession ? (\n          <>\n            <div className=\"text-lg font-medium\">\n              {format(new Date(firstSession.bookingDate), 'HH:mm')} | {firstSession.ownerName} w/ {firstSession.dogName}\n            </div>\n            <div className=\"text-white/80 text-sm\">\n              {firstSession.sessionType} • {format(new Date(firstSession.bookingDate), 'EEEE, d MMMM yyyy')}\n            </div>\n          </>\n        ) : (\n          <div className=\"text-lg font-medium\">No upcoming sessions</div>\n        )}\n      </button>\n\n      <SessionModal\n        session={selectedSession}\n        isOpen={!!selectedSession && !showEditSessionModal && !showEditClientModal}\n        onClose={handleCloseModal}\n        onEditSession={handleEditSession}\n        onEditClient={handleEditClient}\n      />\n\n      <EditSessionModal\n        session={selectedSession}\n        isOpen={showEditSessionModal}\n        onClose={handleCloseEditSessionModal}\n      />\n\n      <EditClientModal\n        client={editingClient}\n        isOpen={showEditClientModal}\n        onClose={handleCloseEditClientModal}\n      />\n\n      <AddModal\n        isOpen={showAddModal}\n        onClose={handleCloseAddModal}\n        type={addModalType}\n      />\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AAXA;;;;;;;;;;;AAae,SAAS;IACtB,MAAM,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,6HAAA,CAAA,SAAM,AAAD;IACvB,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,IAAI;IACnD,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAkB;IACvE,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAwB;IACvE,MAAM,CAAC,sBAAsB,wBAAwB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjE,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/D,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAElE,MAAM,aAAa,CAAA,GAAA,2IAAA,CAAA,eAAY,AAAD,EAAE;IAChC,MAAM,WAAW,CAAA,GAAA,yIAAA,CAAA,aAAU,AAAD,EAAE;IAC5B,MAAM,cAAc,CAAA,GAAA,gJAAA,CAAA,oBAAiB,AAAD,EAAE;QAAE,OAAO;QAAY,KAAK;IAAS;IAEzE,MAAM,oBAAoB,CAAC;QACzB,OAAO,MAAM,QAAQ,CAAC,MAAM,CAAC,CAAA,UAC3B,CAAA,GAAA,wIAAA,CAAA,YAAS,AAAD,EAAE,IAAI,KAAK,QAAQ,WAAW,GAAG;IAE7C;IAEA,MAAM,sBAAsB;QAC1B,eAAe,CAAA,GAAA,wIAAA,CAAA,YAAS,AAAD,EAAE,aAAa;IACxC;IAEA,MAAM,kBAAkB;QACtB,eAAe,CAAA,GAAA,wIAAA,CAAA,YAAS,AAAD,EAAE,aAAa;IACxC;IAEA,MAAM,qBAAqB,CAAC;QAC1B,mBAAmB;IACrB;IAEA,MAAM,mBAAmB;QACvB,mBAAmB;IACrB;IAEA,MAAM,mBAAmB;QACvB,gBAAgB;QAChB,gBAAgB;IAClB;IAEA,MAAM,kBAAkB;QACtB,gBAAgB;QAChB,gBAAgB;IAClB;IAEA,MAAM,sBAAsB;QAC1B,gBAAgB;IAClB;IAEA,MAAM,oBAAoB,CAAC;QACzB,mBAAmB;QACnB,wBAAwB;IAC1B;IAEA,MAAM,mBAAmB,CAAC;QACxB,oDAAoD;QACpD,MAAM,SAAS,MAAM,OAAO,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE,SAAS,KAAK,QAAQ,SAAS;QACxE,IAAI,QAAQ;YACV,iBAAiB;YACjB,uBAAuB;QACzB;IACF;IAEA,MAAM,8BAA8B;QAClC,wBAAwB;IAC1B;IAEA,MAAM,6BAA6B;QACjC,uBAAuB;QACvB,iBAAiB;IACnB;IAEA,MAAM,oBAAoB;QACxB,IAAI,cAAc;YAChB,mBAAmB;QACrB;IACF;IAEA,iCAAiC;IACjC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,gBAAgB,CAAC;YACrB,IAAI,MAAM,GAAG,KAAK,aAAa;gBAC7B,MAAM,cAAc;gBACpB;YACF,OAAO,IAAI,MAAM,GAAG,KAAK,cAAc;gBACrC,MAAM,cAAc;gBACpB;YACF;QACF;QAEA,OAAO,gBAAgB,CAAC,WAAW;QACnC,OAAO,IAAM,OAAO,mBAAmB,CAAC,WAAW;IACrD,GAAG,EAAE;IAEL,6DAA6D;IAC7D,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,oBAAoB,SAAS,cAAc,CAAC;QAClD,IAAI,mBAAmB;YACrB,kBAAkB,KAAK;QACzB;IACF,GAAG,EAAE;IAEL,+CAA+C;IAC/C,MAAM,eAAe,MAAM,QAAQ,CAAC,EAAE;IAEtC,qBACE,8OAAC;QACC,IAAG;QACH,WAAU;QACV,OAAO;YAAE,YAAY;QAAsC;QAC3D,UAAU;;0BAEV,8OAAC,sIAAA,CAAA,UAAM;gBACL,OAAM;gBACN,aAAa;gBACb,eAAc;gBACd,YAAY;gBACZ,UAAU;gBACV,mBAAkB;;;;;;0BAIpB,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CACX,CAAA,GAAA,qJAAA,CAAA,SAAM,AAAD,EAAE,aAAa;;;;;;8CAEvB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CACC,SAAS;4CACT,WAAU;sDAEV,cAAA,8OAAC,oNAAA,CAAA,cAAW;gDAAC,MAAM;;;;;;;;;;;sDAErB,8OAAC;4CACC,SAAS;4CACT,WAAU;sDAEV,cAAA,8OAAC,sNAAA,CAAA,eAAY;gDAAC,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAO5B,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACZ;oCAAC;oCAAO;oCAAO;oCAAO;oCAAO;oCAAO;oCAAO;iCAAM,CAAC,GAAG,CAAC,CAAA,oBACrD,8OAAC;wCAAc,WAAU;kDACtB;uCADO;;;;;;;;;;0CAMd,8OAAC;gCAAI,WAAU;0CACZ,YAAY,GAAG,CAAC,CAAA;oCACf,MAAM,WAAW,kBAAkB;oCACnC,MAAM,YAAY,CAAA,GAAA,qJAAA,CAAA,SAAM,AAAD,EAAE,KAAK;oCAE9B,qBACE,8OAAC;wCAA4B,WAAU;;0DACrC,8OAAC;gDAAI,WAAU;0DAA0C;;;;;;0DACzD,8OAAC;gDAAI,WAAU;;oDACZ,SAAS,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAA,wBACxB,8OAAC;4DAEC,SAAS,IAAM,mBAAmB;4DAClC,WAAU;;gEAET,CAAA,GAAA,qJAAA,CAAA,SAAM,AAAD,EAAE,IAAI,KAAK,QAAQ,WAAW,GAAG;gEAAS;;2DAJ3C,QAAQ,EAAE;;;;;oDAOlB,SAAS,MAAM,GAAG,mBACjB,8OAAC;wDAAI,WAAU;;4DAAmD;4DAC9D,SAAS,MAAM,GAAG;4DAAE;;;;;;;;;;;;;;uCAdpB,IAAI,WAAW;;;;;gCAoB7B;;;;;;;;;;;;;;;;;;0BAMN,8OAAC;gBACC,SAAS;gBACT,UAAU,CAAC;gBACX,WAAU;gBACV,OAAO;oBAAE,eAAe;gBAAyC;0BAEhE,6BACC;;sCACE,8OAAC;4BAAI,WAAU;;gCACZ,CAAA,GAAA,qJAAA,CAAA,SAAM,AAAD,EAAE,IAAI,KAAK,aAAa,WAAW,GAAG;gCAAS;gCAAI,aAAa,SAAS;gCAAC;gCAAK,aAAa,OAAO;;;;;;;sCAE3G,8OAAC;4BAAI,WAAU;;gCACZ,aAAa,WAAW;gCAAC;gCAAI,CAAA,GAAA,qJAAA,CAAA,SAAM,AAAD,EAAE,IAAI,KAAK,aAAa,WAAW,GAAG;;;;;;;;iDAI7E,8OAAC;oBAAI,WAAU;8BAAsB;;;;;;;;;;;0BAIzC,8OAAC,4IAAA,CAAA,UAAY;gBACX,SAAS;gBACT,QAAQ,CAAC,CAAC,mBAAmB,CAAC,wBAAwB,CAAC;gBACvD,SAAS;gBACT,eAAe;gBACf,cAAc;;;;;;0BAGhB,8OAAC,gJAAA,CAAA,UAAgB;gBACf,SAAS;gBACT,QAAQ;gBACR,SAAS;;;;;;0BAGX,8OAAC,+IAAA,CAAA,UAAe;gBACd,QAAQ;gBACR,QAAQ;gBACR,SAAS;;;;;;0BAGX,8OAAC,8HAAA,CAAA,UAAQ;gBACP,QAAQ;gBACR,SAAS;gBACT,MAAM;;;;;;;;;;;;AAId", "debugId": null}}]}