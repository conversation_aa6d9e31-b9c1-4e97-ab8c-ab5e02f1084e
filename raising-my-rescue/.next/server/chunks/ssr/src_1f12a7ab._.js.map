{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/RMR%20App%20Version%202/raising-my-rescue/src/components/layout/Header.tsx"], "sourcesContent": ["'use client';\n\nimport { Search, Plus } from 'lucide-react';\nimport { useState } from 'react';\n\ninterface HeaderProps {\n  title: string;\n  showAddButton?: boolean;\n  onAddClick?: () => void;\n  addButtonText?: string;\n  showSearch?: boolean;\n  onSearch?: (query: string) => void;\n  searchPlaceholder?: string;\n}\n\nexport default function Header({\n  title,\n  showAddButton = false,\n  onAddClick,\n  addButtonText = 'Add',\n  showSearch = false,\n  onSearch,\n  searchPlaceholder = 'Search',\n}: HeaderProps) {\n  const [searchQuery, setSearchQuery] = useState('');\n\n  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {\n    const query = e.target.value;\n    setSearchQuery(query);\n    onSearch?.(query);\n  };\n\n  return (\n    <div className=\"bg-amber-800 text-white px-4 py-3 safe-area-pt\">\n      {/* Top row with title and buttons */}\n      <div className=\"flex items-center justify-between mb-3\">\n        <h1 className=\"text-3xl font-semibold\">{title}</h1>\n        <div className=\"flex items-center gap-2\">\n          {showAddButton && (\n            <button\n              onClick={onAddClick}\n              className=\"bg-white/20 hover:bg-white/30 p-2 rounded-full transition-colors\"\n              title={addButtonText}\n            >\n              <Plus size={20} />\n            </button>\n          )}\n        </div>\n      </div>\n\n      {/* Search bar */}\n      {showSearch && (\n        <div className=\"relative\">\n          <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-white/60\" size={20} />\n          <input\n            type=\"text\"\n            value={searchQuery}\n            onChange={handleSearchChange}\n            placeholder={searchPlaceholder}\n            className=\"w-full bg-white/20 placeholder-white/60 text-white px-10 py-3 rounded-lg focus:outline-none focus:bg-white/30 transition-colors\"\n          />\n        </div>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AACA;AAHA;;;;AAee,SAAS,OAAO,EAC7B,KAAK,EACL,gBAAgB,KAAK,EACrB,UAAU,EACV,gBAAgB,KAAK,EACrB,aAAa,KAAK,EAClB,QAAQ,EACR,oBAAoB,QAAQ,EAChB;IACZ,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,MAAM,qBAAqB,CAAC;QAC1B,MAAM,QAAQ,EAAE,MAAM,CAAC,KAAK;QAC5B,eAAe;QACf,WAAW;IACb;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAA0B;;;;;;kCACxC,8OAAC;wBAAI,WAAU;kCACZ,+BACC,8OAAC;4BACC,SAAS;4BACT,WAAU;4BACV,OAAO;sCAEP,cAAA,8OAAC,kMAAA,CAAA,OAAI;gCAAC,MAAM;;;;;;;;;;;;;;;;;;;;;;YAOnB,4BACC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,sMAAA,CAAA,SAAM;wBAAC,WAAU;wBAAmE,MAAM;;;;;;kCAC3F,8OAAC;wBACC,MAAK;wBACL,OAAO;wBACP,UAAU;wBACV,aAAa;wBACb,WAAU;;;;;;;;;;;;;;;;;;AAMtB", "debugId": null}}, {"offset": {"line": 109, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/RMR%20App%20Version%202/raising-my-rescue/src/components/AddModal.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { X } from 'lucide-react';\nimport { useModal } from '@/context/ModalContext';\n\ninterface AddModalProps {\n  isOpen: boolean;\n  onClose: () => void;\n  type: 'session' | 'client';\n}\n\nexport default function AddModal({ isOpen, onClose, type }: AddModalProps) {\n  const [isVisible, setIsVisible] = useState(false);\n  const [isAnimating, setIsAnimating] = useState(false);\n  const { setModalOpen } = useModal();\n\n  useEffect(() => {\n    if (isOpen) {\n      setIsVisible(true);\n      setModalOpen(true);\n      // Small delay to trigger animation\n      setTimeout(() => setIsAnimating(true), 10);\n    } else {\n      setIsAnimating(false);\n      // Wait for animation to complete before hiding\n      setTimeout(() => {\n        setIsVisible(false);\n        setModalOpen(false);\n      }, 300);\n    }\n  }, [isOpen, setModalOpen]);\n\n  const handleClose = () => {\n    setIsAnimating(false);\n    setTimeout(() => {\n      setIsVisible(false);\n      setModalOpen(false);\n      onClose();\n    }, 300);\n  };\n\n  const handleBackdropClick = (e: React.MouseEvent) => {\n    if (e.target === e.currentTarget) {\n      handleClose();\n    }\n  };\n\n  if (!isVisible) return null;\n\n  return (\n    <div \n      className={`fixed inset-0 z-50 transition-all duration-300 ${\n        isAnimating ? 'bg-black/50' : 'bg-black/0'\n      }`}\n      onClick={handleBackdropClick}\n    >\n      <div \n        className={`fixed bottom-0 left-0 right-0 bg-white rounded-t-3xl shadow-2xl transition-transform duration-300 ease-out ${\n          isAnimating ? 'translate-y-0' : 'translate-y-full'\n        }`}\n        style={{ maxHeight: '90vh' }}\n      >\n        {/* Header */}\n        <div className=\"flex items-center justify-between p-6 border-b border-gray-200\">\n          <h2 className=\"text-xl font-semibold\">\n            {type === 'session' ? 'Add Session' : 'Add Client'}\n          </h2>\n          <button\n            onClick={handleClose}\n            className=\"p-2 hover:bg-gray-100 rounded-full transition-colors\"\n          >\n            <X size={24} />\n          </button>\n        </div>\n\n        {/* Content */}\n        <div className=\"p-6 overflow-y-auto\" style={{ maxHeight: 'calc(90vh - 80px)' }}>\n          {type === 'session' ? (\n            <SessionForm onSubmit={handleClose} />\n          ) : (\n            <ClientForm onSubmit={handleClose} />\n          )}\n        </div>\n      </div>\n    </div>\n  );\n}\n\nfunction SessionForm({ onSubmit }: { onSubmit: () => void }) {\n  const [formData, setFormData] = useState({\n    ownerName: '',\n    dogName: '',\n    sessionType: 'In-Person',\n    date: '',\n    time: '',\n    notes: ''\n  });\n\n  const handleSubmit = (e: React.FormEvent) => {\n    e.preventDefault();\n    // TODO: Implement session creation\n    console.log('Creating session:', formData);\n    onSubmit();\n  };\n\n  return (\n    <form onSubmit={handleSubmit} className=\"space-y-6\">\n      <div>\n        <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n          Owner Name\n        </label>\n        <input\n          type=\"text\"\n          value={formData.ownerName}\n          onChange={(e) => setFormData({ ...formData, ownerName: e.target.value })}\n          className=\"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-transparent\"\n          placeholder=\"Enter owner name\"\n          required\n        />\n      </div>\n\n      <div>\n        <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n          Dog Name\n        </label>\n        <input\n          type=\"text\"\n          value={formData.dogName}\n          onChange={(e) => setFormData({ ...formData, dogName: e.target.value })}\n          className=\"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-transparent\"\n          placeholder=\"Enter dog name\"\n          required\n        />\n      </div>\n\n      <div>\n        <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n          Session Type\n        </label>\n        <select\n          value={formData.sessionType}\n          onChange={(e) => setFormData({ ...formData, sessionType: e.target.value })}\n          className=\"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-transparent\"\n        >\n          <option value=\"In-Person\">In-Person</option>\n          <option value=\"Virtual\">Virtual</option>\n          <option value=\"Phone\">Phone</option>\n        </select>\n      </div>\n\n      <div className=\"grid grid-cols-2 gap-4\">\n        <div>\n          <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n            Date\n          </label>\n          <input\n            type=\"date\"\n            value={formData.date}\n            onChange={(e) => setFormData({ ...formData, date: e.target.value })}\n            className=\"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-transparent\"\n            required\n          />\n        </div>\n\n        <div>\n          <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n            Time\n          </label>\n          <input\n            type=\"time\"\n            value={formData.time}\n            onChange={(e) => setFormData({ ...formData, time: e.target.value })}\n            className=\"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-transparent\"\n            required\n          />\n        </div>\n      </div>\n\n      <div>\n        <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n          Notes (Optional)\n        </label>\n        <textarea\n          value={formData.notes}\n          onChange={(e) => setFormData({ ...formData, notes: e.target.value })}\n          className=\"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-transparent\"\n          placeholder=\"Add any notes about the session\"\n          rows={3}\n        />\n      </div>\n\n      <button\n        type=\"submit\"\n        className=\"w-full bg-amber-800 text-white py-3 px-6 rounded-lg font-medium hover:bg-amber-700 transition-colors\"\n      >\n        Create Session\n      </button>\n    </form>\n  );\n}\n\nfunction ClientForm({ onSubmit }: { onSubmit: () => void }) {\n  const [formData, setFormData] = useState({\n    ownerName: '',\n    email: '',\n    phone: '',\n    dogName: '',\n    dogBreed: '',\n    dogAge: '',\n    notes: ''\n  });\n\n  const handleSubmit = (e: React.FormEvent) => {\n    e.preventDefault();\n    // TODO: Implement client creation\n    console.log('Creating client:', formData);\n    onSubmit();\n  };\n\n  return (\n    <form onSubmit={handleSubmit} className=\"space-y-6\">\n      <div>\n        <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n          Owner Name\n        </label>\n        <input\n          type=\"text\"\n          value={formData.ownerName}\n          onChange={(e) => setFormData({ ...formData, ownerName: e.target.value })}\n          className=\"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-transparent\"\n          placeholder=\"Enter owner name\"\n          required\n        />\n      </div>\n\n      <div className=\"grid grid-cols-1 gap-4\">\n        <div>\n          <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n            Email\n          </label>\n          <input\n            type=\"email\"\n            value={formData.email}\n            onChange={(e) => setFormData({ ...formData, email: e.target.value })}\n            className=\"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-transparent\"\n            placeholder=\"Enter email address\"\n            required\n          />\n        </div>\n\n        <div>\n          <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n            Phone\n          </label>\n          <input\n            type=\"tel\"\n            value={formData.phone}\n            onChange={(e) => setFormData({ ...formData, phone: e.target.value })}\n            className=\"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-transparent\"\n            placeholder=\"Enter phone number\"\n          />\n        </div>\n      </div>\n\n      <div>\n        <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n          Dog Name\n        </label>\n        <input\n          type=\"text\"\n          value={formData.dogName}\n          onChange={(e) => setFormData({ ...formData, dogName: e.target.value })}\n          className=\"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-transparent\"\n          placeholder=\"Enter dog name\"\n          required\n        />\n      </div>\n\n      <div className=\"grid grid-cols-2 gap-4\">\n        <div>\n          <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n            Breed\n          </label>\n          <input\n            type=\"text\"\n            value={formData.dogBreed}\n            onChange={(e) => setFormData({ ...formData, dogBreed: e.target.value })}\n            className=\"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-transparent\"\n            placeholder=\"Enter dog breed\"\n          />\n        </div>\n\n        <div>\n          <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n            Age\n          </label>\n          <input\n            type=\"text\"\n            value={formData.dogAge}\n            onChange={(e) => setFormData({ ...formData, dogAge: e.target.value })}\n            className=\"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-transparent\"\n            placeholder=\"e.g., 2 years\"\n          />\n        </div>\n      </div>\n\n      <div>\n        <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n          Notes (Optional)\n        </label>\n        <textarea\n          value={formData.notes}\n          onChange={(e) => setFormData({ ...formData, notes: e.target.value })}\n          className=\"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-transparent\"\n          placeholder=\"Add any notes about the client or dog\"\n          rows={3}\n        />\n      </div>\n\n      <button\n        type=\"submit\"\n        className=\"w-full bg-amber-800 text-white py-3 px-6 rounded-lg font-medium hover:bg-amber-700 transition-colors\"\n      >\n        Create Client\n      </button>\n    </form>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAYe,SAAS,SAAS,EAAE,MAAM,EAAE,OAAO,EAAE,IAAI,EAAiB;IACvE,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,EAAE,YAAY,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,WAAQ,AAAD;IAEhC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,QAAQ;YACV,aAAa;YACb,aAAa;YACb,mCAAmC;YACnC,WAAW,IAAM,eAAe,OAAO;QACzC,OAAO;YACL,eAAe;YACf,+CAA+C;YAC/C,WAAW;gBACT,aAAa;gBACb,aAAa;YACf,GAAG;QACL;IACF,GAAG;QAAC;QAAQ;KAAa;IAEzB,MAAM,cAAc;QAClB,eAAe;QACf,WAAW;YACT,aAAa;YACb,aAAa;YACb;QACF,GAAG;IACL;IAEA,MAAM,sBAAsB,CAAC;QAC3B,IAAI,EAAE,MAAM,KAAK,EAAE,aAAa,EAAE;YAChC;QACF;IACF;IAEA,IAAI,CAAC,WAAW,OAAO;IAEvB,qBACE,8OAAC;QACC,WAAW,CAAC,+CAA+C,EACzD,cAAc,gBAAgB,cAC9B;QACF,SAAS;kBAET,cAAA,8OAAC;YACC,WAAW,CAAC,2GAA2G,EACrH,cAAc,kBAAkB,oBAChC;YACF,OAAO;gBAAE,WAAW;YAAO;;8BAG3B,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCACX,SAAS,YAAY,gBAAgB;;;;;;sCAExC,8OAAC;4BACC,SAAS;4BACT,WAAU;sCAEV,cAAA,8OAAC,4LAAA,CAAA,IAAC;gCAAC,MAAM;;;;;;;;;;;;;;;;;8BAKb,8OAAC;oBAAI,WAAU;oBAAsB,OAAO;wBAAE,WAAW;oBAAoB;8BAC1E,SAAS,0BACR,8OAAC;wBAAY,UAAU;;;;;6CAEvB,8OAAC;wBAAW,UAAU;;;;;;;;;;;;;;;;;;;;;;AAMlC;AAEA,SAAS,YAAY,EAAE,QAAQ,EAA4B;IACzD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,WAAW;QACX,SAAS;QACT,aAAa;QACb,MAAM;QACN,MAAM;QACN,OAAO;IACT;IAEA,MAAM,eAAe,CAAC;QACpB,EAAE,cAAc;QAChB,mCAAmC;QACnC,QAAQ,GAAG,CAAC,qBAAqB;QACjC;IACF;IAEA,qBACE,8OAAC;QAAK,UAAU;QAAc,WAAU;;0BACtC,8OAAC;;kCACC,8OAAC;wBAAM,WAAU;kCAA+C;;;;;;kCAGhE,8OAAC;wBACC,MAAK;wBACL,OAAO,SAAS,SAAS;wBACzB,UAAU,CAAC,IAAM,YAAY;gCAAE,GAAG,QAAQ;gCAAE,WAAW,EAAE,MAAM,CAAC,KAAK;4BAAC;wBACtE,WAAU;wBACV,aAAY;wBACZ,QAAQ;;;;;;;;;;;;0BAIZ,8OAAC;;kCACC,8OAAC;wBAAM,WAAU;kCAA+C;;;;;;kCAGhE,8OAAC;wBACC,MAAK;wBACL,OAAO,SAAS,OAAO;wBACvB,UAAU,CAAC,IAAM,YAAY;gCAAE,GAAG,QAAQ;gCAAE,SAAS,EAAE,MAAM,CAAC,KAAK;4BAAC;wBACpE,WAAU;wBACV,aAAY;wBACZ,QAAQ;;;;;;;;;;;;0BAIZ,8OAAC;;kCACC,8OAAC;wBAAM,WAAU;kCAA+C;;;;;;kCAGhE,8OAAC;wBACC,OAAO,SAAS,WAAW;wBAC3B,UAAU,CAAC,IAAM,YAAY;gCAAE,GAAG,QAAQ;gCAAE,aAAa,EAAE,MAAM,CAAC,KAAK;4BAAC;wBACxE,WAAU;;0CAEV,8OAAC;gCAAO,OAAM;0CAAY;;;;;;0CAC1B,8OAAC;gCAAO,OAAM;0CAAU;;;;;;0CACxB,8OAAC;gCAAO,OAAM;0CAAQ;;;;;;;;;;;;;;;;;;0BAI1B,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;;0CACC,8OAAC;gCAAM,WAAU;0CAA+C;;;;;;0CAGhE,8OAAC;gCACC,MAAK;gCACL,OAAO,SAAS,IAAI;gCACpB,UAAU,CAAC,IAAM,YAAY;wCAAE,GAAG,QAAQ;wCAAE,MAAM,EAAE,MAAM,CAAC,KAAK;oCAAC;gCACjE,WAAU;gCACV,QAAQ;;;;;;;;;;;;kCAIZ,8OAAC;;0CACC,8OAAC;gCAAM,WAAU;0CAA+C;;;;;;0CAGhE,8OAAC;gCACC,MAAK;gCACL,OAAO,SAAS,IAAI;gCACpB,UAAU,CAAC,IAAM,YAAY;wCAAE,GAAG,QAAQ;wCAAE,MAAM,EAAE,MAAM,CAAC,KAAK;oCAAC;gCACjE,WAAU;gCACV,QAAQ;;;;;;;;;;;;;;;;;;0BAKd,8OAAC;;kCACC,8OAAC;wBAAM,WAAU;kCAA+C;;;;;;kCAGhE,8OAAC;wBACC,OAAO,SAAS,KAAK;wBACrB,UAAU,CAAC,IAAM,YAAY;gCAAE,GAAG,QAAQ;gCAAE,OAAO,EAAE,MAAM,CAAC,KAAK;4BAAC;wBAClE,WAAU;wBACV,aAAY;wBACZ,MAAM;;;;;;;;;;;;0BAIV,8OAAC;gBACC,MAAK;gBACL,WAAU;0BACX;;;;;;;;;;;;AAKP;AAEA,SAAS,WAAW,EAAE,QAAQ,EAA4B;IACxD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,WAAW;QACX,OAAO;QACP,OAAO;QACP,SAAS;QACT,UAAU;QACV,QAAQ;QACR,OAAO;IACT;IAEA,MAAM,eAAe,CAAC;QACpB,EAAE,cAAc;QAChB,kCAAkC;QAClC,QAAQ,GAAG,CAAC,oBAAoB;QAChC;IACF;IAEA,qBACE,8OAAC;QAAK,UAAU;QAAc,WAAU;;0BACtC,8OAAC;;kCACC,8OAAC;wBAAM,WAAU;kCAA+C;;;;;;kCAGhE,8OAAC;wBACC,MAAK;wBACL,OAAO,SAAS,SAAS;wBACzB,UAAU,CAAC,IAAM,YAAY;gCAAE,GAAG,QAAQ;gCAAE,WAAW,EAAE,MAAM,CAAC,KAAK;4BAAC;wBACtE,WAAU;wBACV,aAAY;wBACZ,QAAQ;;;;;;;;;;;;0BAIZ,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;;0CACC,8OAAC;gCAAM,WAAU;0CAA+C;;;;;;0CAGhE,8OAAC;gCACC,MAAK;gCACL,OAAO,SAAS,KAAK;gCACrB,UAAU,CAAC,IAAM,YAAY;wCAAE,GAAG,QAAQ;wCAAE,OAAO,EAAE,MAAM,CAAC,KAAK;oCAAC;gCAClE,WAAU;gCACV,aAAY;gCACZ,QAAQ;;;;;;;;;;;;kCAIZ,8OAAC;;0CACC,8OAAC;gCAAM,WAAU;0CAA+C;;;;;;0CAGhE,8OAAC;gCACC,MAAK;gCACL,OAAO,SAAS,KAAK;gCACrB,UAAU,CAAC,IAAM,YAAY;wCAAE,GAAG,QAAQ;wCAAE,OAAO,EAAE,MAAM,CAAC,KAAK;oCAAC;gCAClE,WAAU;gCACV,aAAY;;;;;;;;;;;;;;;;;;0BAKlB,8OAAC;;kCACC,8OAAC;wBAAM,WAAU;kCAA+C;;;;;;kCAGhE,8OAAC;wBACC,MAAK;wBACL,OAAO,SAAS,OAAO;wBACvB,UAAU,CAAC,IAAM,YAAY;gCAAE,GAAG,QAAQ;gCAAE,SAAS,EAAE,MAAM,CAAC,KAAK;4BAAC;wBACpE,WAAU;wBACV,aAAY;wBACZ,QAAQ;;;;;;;;;;;;0BAIZ,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;;0CACC,8OAAC;gCAAM,WAAU;0CAA+C;;;;;;0CAGhE,8OAAC;gCACC,MAAK;gCACL,OAAO,SAAS,QAAQ;gCACxB,UAAU,CAAC,IAAM,YAAY;wCAAE,GAAG,QAAQ;wCAAE,UAAU,EAAE,MAAM,CAAC,KAAK;oCAAC;gCACrE,WAAU;gCACV,aAAY;;;;;;;;;;;;kCAIhB,8OAAC;;0CACC,8OAAC;gCAAM,WAAU;0CAA+C;;;;;;0CAGhE,8OAAC;gCACC,MAAK;gCACL,OAAO,SAAS,MAAM;gCACtB,UAAU,CAAC,IAAM,YAAY;wCAAE,GAAG,QAAQ;wCAAE,QAAQ,EAAE,MAAM,CAAC,KAAK;oCAAC;gCACnE,WAAU;gCACV,aAAY;;;;;;;;;;;;;;;;;;0BAKlB,8OAAC;;kCACC,8OAAC;wBAAM,WAAU;kCAA+C;;;;;;kCAGhE,8OAAC;wBACC,OAAO,SAAS,KAAK;wBACrB,UAAU,CAAC,IAAM,YAAY;gCAAE,GAAG,QAAQ;gCAAE,OAAO,EAAE,MAAM,CAAC,KAAK;4BAAC;wBAClE,WAAU;wBACV,aAAY;wBACZ,MAAM;;;;;;;;;;;;0BAIV,8OAAC;gBACC,MAAK;gBACL,WAAU;0BACX;;;;;;;;;;;;AAKP", "debugId": null}}, {"offset": {"line": 756, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/RMR%20App%20Version%202/raising-my-rescue/src/components/modals/SlideUpModal.tsx"], "sourcesContent": ["'use client';\n\nimport { ReactNode, useEffect } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { X } from 'lucide-react';\nimport { useModal } from '@/context/ModalContext';\n\ninterface SlideUpModalProps {\n  isOpen: boolean;\n  onClose: () => void;\n  title: string;\n  children: ReactNode;\n}\n\nexport default function SlideUpModal({ isOpen, onClose, title, children }: SlideUpModalProps) {\n  const { registerModal, unregisterModal } = useModal();\n\n  useEffect(() => {\n    const modalId = `slide-up-modal-${Date.now()}`;\n\n    if (isOpen) {\n      document.body.style.overflow = 'hidden';\n      registerModal(modalId);\n    } else {\n      document.body.style.overflow = 'unset';\n      unregisterModal(modalId);\n    }\n\n    return () => {\n      document.body.style.overflow = 'unset';\n      unregisterModal(modalId);\n    };\n  }, [isOpen, registerModal, unregisterModal]);\n\n  return (\n    <AnimatePresence>\n      {isOpen && (\n        <>\n          {/* Backdrop */}\n          <motion.div\n            initial={{ opacity: 0 }}\n            animate={{ opacity: 1 }}\n            exit={{ opacity: 0 }}\n            onClick={onClose}\n            className=\"fixed inset-0 bg-black/50 z-40\"\n          />\n\n          {/* Modal */}\n          <motion.div\n            initial={{ y: '100%' }}\n            animate={{ y: 0 }}\n            exit={{ y: '100%' }}\n            transition={{ type: 'spring', damping: 30, stiffness: 300 }}\n            className=\"fixed bottom-0 left-0 right-0 bg-amber-800 text-white rounded-t-3xl z-50 max-h-[85vh] overflow-hidden\"\n          >\n            {/* Handle bar */}\n            <div className=\"flex justify-center py-3\">\n              <div className=\"w-12 h-1 bg-white/30 rounded-full\" />\n            </div>\n\n            {/* Header */}\n            <div className=\"flex items-center justify-between px-6 pb-4\">\n              <h2 className=\"text-lg font-semibold\">{title}</h2>\n              <button\n                onClick={onClose}\n                className=\"p-2 hover:bg-white/20 rounded-full transition-colors\"\n              >\n                <X size={20} />\n              </button>\n            </div>\n\n            {/* Content */}\n            <div className=\"px-6 pb-6 overflow-y-auto max-h-[calc(85vh-120px)]\">\n              {children}\n            </div>\n          </motion.div>\n        </>\n      )}\n    </AnimatePresence>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AACA;AALA;;;;;;AAce,SAAS,aAAa,EAAE,MAAM,EAAE,OAAO,EAAE,KAAK,EAAE,QAAQ,EAAqB;IAC1F,MAAM,EAAE,aAAa,EAAE,eAAe,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,WAAQ,AAAD;IAElD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,UAAU,CAAC,eAAe,EAAE,KAAK,GAAG,IAAI;QAE9C,IAAI,QAAQ;YACV,SAAS,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG;YAC/B,cAAc;QAChB,OAAO;YACL,SAAS,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG;YAC/B,gBAAgB;QAClB;QAEA,OAAO;YACL,SAAS,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG;YAC/B,gBAAgB;QAClB;IACF,GAAG;QAAC;QAAQ;QAAe;KAAgB;IAE3C,qBACE,8OAAC,yLAAA,CAAA,kBAAe;kBACb,wBACC;;8BAEE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;oBAAE;oBACtB,SAAS;wBAAE,SAAS;oBAAE;oBACtB,MAAM;wBAAE,SAAS;oBAAE;oBACnB,SAAS;oBACT,WAAU;;;;;;8BAIZ,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,GAAG;oBAAO;oBACrB,SAAS;wBAAE,GAAG;oBAAE;oBAChB,MAAM;wBAAE,GAAG;oBAAO;oBAClB,YAAY;wBAAE,MAAM;wBAAU,SAAS;wBAAI,WAAW;oBAAI;oBAC1D,WAAU;;sCAGV,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;;;;;;;;;;sCAIjB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAyB;;;;;;8CACvC,8OAAC;oCACC,SAAS;oCACT,WAAU;8CAEV,cAAA,8OAAC,4LAAA,CAAA,IAAC;wCAAC,MAAM;;;;;;;;;;;;;;;;;sCAKb,8OAAC;4BAAI,WAAU;sCACZ;;;;;;;;;;;;;;;;;;;AAOf", "debugId": null}}, {"offset": {"line": 902, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/RMR%20App%20Version%202/raising-my-rescue/src/components/modals/ClientModal.tsx"], "sourcesContent": ["'use client';\n\nimport { Client } from '@/types';\nimport { useApp } from '@/context/AppContext';\nimport SlideUpModal from './SlideUpModal';\n\ninterface ClientModalProps {\n  client: Client | null;\n  isOpen: boolean;\n  onClose: () => void;\n  onEditClient: (client: Client) => void;\n}\n\nexport default function ClientModal({ client, isOpen, onClose, onEditClient }: ClientModalProps) {\n  const { dispatch } = useApp();\n\n  if (!client) return null;\n\n  const handleDelete = () => {\n    dispatch({ type: 'DELETE_CLIENT', payload: client.id });\n    onClose();\n  };\n\n  const handleToggleActive = () => {\n    dispatch({\n      type: 'UPDATE_CLIENT',\n      payload: { ...client, active: !client.active }\n    });\n  };\n\n  const handleEditClick = () => {\n    onEditClient(client);\n    onClose();\n  };\n\n  const getAvatarText = (ownerName: string) => {\n    return ownerName.split(' ').map(name => name[0]).join('').toUpperCase();\n  };\n\n  return (\n    <SlideUpModal\n      isOpen={isOpen}\n      onClose={onClose}\n      title={client.ownerName}\n    >\n      <div className=\"space-y-6\">\n        {/* Client Avatar */}\n        <div className=\"flex justify-center\">\n          <div className=\"w-20 h-20 bg-amber-800 rounded-full flex items-center justify-center text-white text-2xl font-medium\">\n            {client.avatar || getAvatarText(client.ownerName)}\n          </div>\n        </div>\n\n        {/* Action Buttons */}\n        <div className=\"flex gap-3\">\n          <button\n            onClick={handleEditClick}\n            className=\"flex-1 bg-amber-800 hover:bg-amber-700 text-white py-3 px-4 rounded-lg font-medium transition-colors\"\n          >\n            Edit Client\n          </button>\n        </div>\n\n        {/* Client Details */}\n        <div className=\"space-y-4\">\n          <div className=\"flex justify-between items-center\">\n            <span className=\"text-gray-600\">Owner Name</span>\n            <span className=\"font-medium text-gray-900\">{client.ownerName}</span>\n          </div>\n\n          {client.email && (\n            <div className=\"flex justify-between items-center\">\n              <span className=\"text-gray-600\">Email</span>\n              <span className=\"font-medium text-gray-900\">{client.email}</span>\n            </div>\n          )}\n\n          {client.phone && (\n            <div className=\"flex justify-between items-center\">\n              <span className=\"text-gray-600\">Phone</span>\n              <span className=\"font-medium text-gray-900\">{client.phone}</span>\n            </div>\n          )}\n\n          {client.dogName && (\n            <div className=\"flex justify-between items-center\">\n              <span className=\"text-gray-600\">Dog Name</span>\n              <span className=\"font-medium text-gray-900\">{client.dogName}</span>\n            </div>\n          )}\n\n          {client.dogBreed && (\n            <div className=\"flex justify-between items-center\">\n              <span className=\"text-gray-600\">Dog Breed</span>\n              <span className=\"font-medium text-gray-900\">{client.dogBreed}</span>\n            </div>\n          )}\n\n          {client.dogAge && (\n            <div className=\"flex justify-between items-center\">\n              <span className=\"text-gray-600\">Dog Age</span>\n              <span className=\"font-medium text-gray-900\">{client.dogAge}</span>\n            </div>\n          )}\n\n          {/* Toggle Switch */}\n          <div className=\"flex justify-between items-center\">\n            <span className=\"text-gray-700 font-medium\">Active Client</span>\n            <button\n              onClick={handleToggleActive}\n              className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${\n                client.active ? 'bg-green-500' : 'bg-gray-300'\n              }`}\n            >\n              <span\n                className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${\n                  client.active ? 'translate-x-6' : 'translate-x-1'\n                }`}\n              />\n            </button>\n          </div>\n        </div>\n\n        {/* Delete Button */}\n        <button\n          onClick={handleDelete}\n          className=\"w-full bg-red-600 hover:bg-red-700 py-3 px-4 rounded-lg font-medium transition-colors mt-6\"\n        >\n          Delete Client\n        </button>\n      </div>\n    </SlideUpModal>\n  );\n}\n"], "names": [], "mappings": ";;;;AAGA;AACA;AAJA;;;;AAae,SAAS,YAAY,EAAE,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,YAAY,EAAoB;IAC7F,MAAM,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,6HAAA,CAAA,SAAM,AAAD;IAE1B,IAAI,CAAC,QAAQ,OAAO;IAEpB,MAAM,eAAe;QACnB,SAAS;YAAE,MAAM;YAAiB,SAAS,OAAO,EAAE;QAAC;QACrD;IACF;IAEA,MAAM,qBAAqB;QACzB,SAAS;YACP,MAAM;YACN,SAAS;gBAAE,GAAG,MAAM;gBAAE,QAAQ,CAAC,OAAO,MAAM;YAAC;QAC/C;IACF;IAEA,MAAM,kBAAkB;QACtB,aAAa;QACb;IACF;IAEA,MAAM,gBAAgB,CAAC;QACrB,OAAO,UAAU,KAAK,CAAC,KAAK,GAAG,CAAC,CAAA,OAAQ,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,IAAI,WAAW;IACvE;IAEA,qBACE,8OAAC,4IAAA,CAAA,UAAY;QACX,QAAQ;QACR,SAAS;QACT,OAAO,OAAO,SAAS;kBAEvB,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;kCACZ,OAAO,MAAM,IAAI,cAAc,OAAO,SAAS;;;;;;;;;;;8BAKpD,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBACC,SAAS;wBACT,WAAU;kCACX;;;;;;;;;;;8BAMH,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAK,WAAU;8CAAgB;;;;;;8CAChC,8OAAC;oCAAK,WAAU;8CAA6B,OAAO,SAAS;;;;;;;;;;;;wBAG9D,OAAO,KAAK,kBACX,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAK,WAAU;8CAAgB;;;;;;8CAChC,8OAAC;oCAAK,WAAU;8CAA6B,OAAO,KAAK;;;;;;;;;;;;wBAI5D,OAAO,KAAK,kBACX,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAK,WAAU;8CAAgB;;;;;;8CAChC,8OAAC;oCAAK,WAAU;8CAA6B,OAAO,KAAK;;;;;;;;;;;;wBAI5D,OAAO,OAAO,kBACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAK,WAAU;8CAAgB;;;;;;8CAChC,8OAAC;oCAAK,WAAU;8CAA6B,OAAO,OAAO;;;;;;;;;;;;wBAI9D,OAAO,QAAQ,kBACd,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAK,WAAU;8CAAgB;;;;;;8CAChC,8OAAC;oCAAK,WAAU;8CAA6B,OAAO,QAAQ;;;;;;;;;;;;wBAI/D,OAAO,MAAM,kBACZ,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAK,WAAU;8CAAgB;;;;;;8CAChC,8OAAC;oCAAK,WAAU;8CAA6B,OAAO,MAAM;;;;;;;;;;;;sCAK9D,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAK,WAAU;8CAA4B;;;;;;8CAC5C,8OAAC;oCACC,SAAS;oCACT,WAAW,CAAC,0EAA0E,EACpF,OAAO,MAAM,GAAG,iBAAiB,eACjC;8CAEF,cAAA,8OAAC;wCACC,WAAW,CAAC,0EAA0E,EACpF,OAAO,MAAM,GAAG,kBAAkB,iBAClC;;;;;;;;;;;;;;;;;;;;;;;8BAOV,8OAAC;oBACC,SAAS;oBACT,WAAU;8BACX;;;;;;;;;;;;;;;;;AAMT", "debugId": null}}, {"offset": {"line": 1194, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/RMR%20App%20Version%202/raising-my-rescue/src/components/modals/EditClientModal.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { Client } from '@/types';\nimport { useApp } from '@/context/AppContext';\nimport SlideUpModal from './SlideUpModal';\n\ninterface EditClientModalProps {\n  client: Client | null;\n  isOpen: boolean;\n  onClose: () => void;\n}\n\nexport default function EditClientModal({ client, isOpen, onClose }: EditClientModalProps) {\n  const { dispatch } = useApp();\n  const [formData, setFormData] = useState({\n    ownerName: '',\n    email: '',\n    phone: '',\n    dogName: '',\n    dogBreed: '',\n    dogAge: '',\n    active: true,\n    notes: ''\n  });\n\n  useEffect(() => {\n    if (client) {\n      setFormData({\n        ownerName: client.ownerName,\n        email: client.email || '',\n        phone: client.phone || '',\n        dogName: client.dogName || '',\n        dogBreed: client.dogBreed || '',\n        dogAge: client.dogAge || '',\n        active: client.active,\n        notes: client.notes || ''\n      });\n    }\n  }, [client]);\n\n  const handleSubmit = (e: React.FormEvent) => {\n    e.preventDefault();\n    if (!client) return;\n\n    const updatedClient: Client = {\n      ...client,\n      ownerName: formData.ownerName,\n      email: formData.email || undefined,\n      phone: formData.phone || undefined,\n      dogName: formData.dogName || undefined,\n      dogBreed: formData.dogBreed || undefined,\n      dogAge: formData.dogAge || undefined,\n      active: formData.active,\n      notes: formData.notes || undefined\n    };\n\n    dispatch({ type: 'UPDATE_CLIENT', payload: updatedClient });\n    onClose();\n  };\n\n  if (!client) return null;\n\n  return (\n    <SlideUpModal\n      isOpen={isOpen}\n      onClose={onClose}\n      title=\"Edit Client\"\n    >\n      <form onSubmit={handleSubmit} className=\"space-y-6\">\n        <div>\n          <label className=\"block text-gray-700 text-sm font-medium mb-2\">\n            Owner Name\n          </label>\n          <input\n            type=\"text\"\n            value={formData.ownerName}\n            onChange={(e) => setFormData({ ...formData, ownerName: e.target.value })}\n            className=\"w-full px-4 py-3 bg-gray-50 border border-gray-300 rounded-lg text-gray-900 placeholder-gray-500 focus:ring-2 focus:ring-amber-500 focus:border-transparent\"\n            placeholder=\"Enter owner name\"\n            required\n          />\n        </div>\n\n        <div>\n          <label className=\"block text-gray-700 text-sm font-medium mb-2\">\n            Email (Optional)\n          </label>\n          <input\n            type=\"email\"\n            value={formData.email}\n            onChange={(e) => setFormData({ ...formData, email: e.target.value })}\n            className=\"w-full px-4 py-3 bg-gray-50 border border-gray-300 rounded-lg text-gray-900 placeholder-gray-500 focus:ring-2 focus:ring-amber-500 focus:border-transparent\"\n            placeholder=\"Enter email address\"\n          />\n        </div>\n\n        <div>\n          <label className=\"block text-gray-700 text-sm font-medium mb-2\">\n            Phone (Optional)\n          </label>\n          <input\n            type=\"tel\"\n            value={formData.phone}\n            onChange={(e) => setFormData({ ...formData, phone: e.target.value })}\n            className=\"w-full px-4 py-3 bg-gray-50 border border-gray-300 rounded-lg text-gray-900 placeholder-gray-500 focus:ring-2 focus:ring-amber-500 focus:border-transparent\"\n            placeholder=\"Enter phone number\"\n          />\n        </div>\n\n        <div>\n          <label className=\"block text-gray-700 text-sm font-medium mb-2\">\n            Dog Name (Optional)\n          </label>\n          <input\n            type=\"text\"\n            value={formData.dogName}\n            onChange={(e) => setFormData({ ...formData, dogName: e.target.value })}\n            className=\"w-full px-4 py-3 bg-gray-50 border border-gray-300 rounded-lg text-gray-900 placeholder-gray-500 focus:ring-2 focus:ring-amber-500 focus:border-transparent\"\n            placeholder=\"Enter dog name\"\n          />\n        </div>\n\n        <div className=\"grid grid-cols-2 gap-4\">\n          <div>\n            <label className=\"block text-gray-700 text-sm font-medium mb-2\">\n              Dog Breed (Optional)\n            </label>\n            <input\n              type=\"text\"\n              value={formData.dogBreed}\n              onChange={(e) => setFormData({ ...formData, dogBreed: e.target.value })}\n              className=\"w-full px-4 py-3 bg-gray-50 border border-gray-300 rounded-lg text-gray-900 placeholder-gray-500 focus:ring-2 focus:ring-amber-500 focus:border-transparent\"\n              placeholder=\"Enter breed\"\n            />\n          </div>\n          <div>\n            <label className=\"block text-gray-700 text-sm font-medium mb-2\">\n              Dog Age (Optional)\n            </label>\n            <input\n              type=\"text\"\n              value={formData.dogAge}\n              onChange={(e) => setFormData({ ...formData, dogAge: e.target.value })}\n              className=\"w-full px-4 py-3 bg-gray-50 border border-gray-300 rounded-lg text-gray-900 placeholder-gray-500 focus:ring-2 focus:ring-amber-500 focus:border-transparent\"\n              placeholder=\"Enter age\"\n            />\n          </div>\n        </div>\n\n        <div className=\"flex justify-between items-center\">\n          <span className=\"text-gray-700 font-medium\">Active Client</span>\n          <button\n            type=\"button\"\n            onClick={() => setFormData({ ...formData, active: !formData.active })}\n            className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${\n              formData.active ? 'bg-green-500' : 'bg-gray-300'\n            }`}\n          >\n            <span\n              className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${\n                formData.active ? 'translate-x-6' : 'translate-x-1'\n              }`}\n            />\n          </button>\n        </div>\n\n        <div>\n          <label className=\"block text-gray-700 text-sm font-medium mb-2\">\n            Notes (Optional)\n          </label>\n          <textarea\n            value={formData.notes}\n            onChange={(e) => setFormData({ ...formData, notes: e.target.value })}\n            className=\"w-full px-4 py-3 bg-gray-50 border border-gray-300 rounded-lg text-gray-900 placeholder-gray-500 focus:ring-2 focus:ring-amber-500 focus:border-transparent\"\n            placeholder=\"Add any notes about the client\"\n            rows={3}\n          />\n        </div>\n\n        <button\n          type=\"submit\"\n          className=\"w-full bg-amber-800 text-white py-3 px-6 rounded-lg font-medium hover:bg-amber-700 transition-colors\"\n        >\n          Update Client\n        </button>\n      </form>\n    </SlideUpModal>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AACA;AALA;;;;;AAae,SAAS,gBAAgB,EAAE,MAAM,EAAE,MAAM,EAAE,OAAO,EAAwB;IACvF,MAAM,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,6HAAA,CAAA,SAAM,AAAD;IAC1B,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,WAAW;QACX,OAAO;QACP,OAAO;QACP,SAAS;QACT,UAAU;QACV,QAAQ;QACR,QAAQ;QACR,OAAO;IACT;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,QAAQ;YACV,YAAY;gBACV,WAAW,OAAO,SAAS;gBAC3B,OAAO,OAAO,KAAK,IAAI;gBACvB,OAAO,OAAO,KAAK,IAAI;gBACvB,SAAS,OAAO,OAAO,IAAI;gBAC3B,UAAU,OAAO,QAAQ,IAAI;gBAC7B,QAAQ,OAAO,MAAM,IAAI;gBACzB,QAAQ,OAAO,MAAM;gBACrB,OAAO,OAAO,KAAK,IAAI;YACzB;QACF;IACF,GAAG;QAAC;KAAO;IAEX,MAAM,eAAe,CAAC;QACpB,EAAE,cAAc;QAChB,IAAI,CAAC,QAAQ;QAEb,MAAM,gBAAwB;YAC5B,GAAG,MAAM;YACT,WAAW,SAAS,SAAS;YAC7B,OAAO,SAAS,KAAK,IAAI;YACzB,OAAO,SAAS,KAAK,IAAI;YACzB,SAAS,SAAS,OAAO,IAAI;YAC7B,UAAU,SAAS,QAAQ,IAAI;YAC/B,QAAQ,SAAS,MAAM,IAAI;YAC3B,QAAQ,SAAS,MAAM;YACvB,OAAO,SAAS,KAAK,IAAI;QAC3B;QAEA,SAAS;YAAE,MAAM;YAAiB,SAAS;QAAc;QACzD;IACF;IAEA,IAAI,CAAC,QAAQ,OAAO;IAEpB,qBACE,8OAAC,4IAAA,CAAA,UAAY;QACX,QAAQ;QACR,SAAS;QACT,OAAM;kBAEN,cAAA,8OAAC;YAAK,UAAU;YAAc,WAAU;;8BACtC,8OAAC;;sCACC,8OAAC;4BAAM,WAAU;sCAA+C;;;;;;sCAGhE,8OAAC;4BACC,MAAK;4BACL,OAAO,SAAS,SAAS;4BACzB,UAAU,CAAC,IAAM,YAAY;oCAAE,GAAG,QAAQ;oCAAE,WAAW,EAAE,MAAM,CAAC,KAAK;gCAAC;4BACtE,WAAU;4BACV,aAAY;4BACZ,QAAQ;;;;;;;;;;;;8BAIZ,8OAAC;;sCACC,8OAAC;4BAAM,WAAU;sCAA+C;;;;;;sCAGhE,8OAAC;4BACC,MAAK;4BACL,OAAO,SAAS,KAAK;4BACrB,UAAU,CAAC,IAAM,YAAY;oCAAE,GAAG,QAAQ;oCAAE,OAAO,EAAE,MAAM,CAAC,KAAK;gCAAC;4BAClE,WAAU;4BACV,aAAY;;;;;;;;;;;;8BAIhB,8OAAC;;sCACC,8OAAC;4BAAM,WAAU;sCAA+C;;;;;;sCAGhE,8OAAC;4BACC,MAAK;4BACL,OAAO,SAAS,KAAK;4BACrB,UAAU,CAAC,IAAM,YAAY;oCAAE,GAAG,QAAQ;oCAAE,OAAO,EAAE,MAAM,CAAC,KAAK;gCAAC;4BAClE,WAAU;4BACV,aAAY;;;;;;;;;;;;8BAIhB,8OAAC;;sCACC,8OAAC;4BAAM,WAAU;sCAA+C;;;;;;sCAGhE,8OAAC;4BACC,MAAK;4BACL,OAAO,SAAS,OAAO;4BACvB,UAAU,CAAC,IAAM,YAAY;oCAAE,GAAG,QAAQ;oCAAE,SAAS,EAAE,MAAM,CAAC,KAAK;gCAAC;4BACpE,WAAU;4BACV,aAAY;;;;;;;;;;;;8BAIhB,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;;8CACC,8OAAC;oCAAM,WAAU;8CAA+C;;;;;;8CAGhE,8OAAC;oCACC,MAAK;oCACL,OAAO,SAAS,QAAQ;oCACxB,UAAU,CAAC,IAAM,YAAY;4CAAE,GAAG,QAAQ;4CAAE,UAAU,EAAE,MAAM,CAAC,KAAK;wCAAC;oCACrE,WAAU;oCACV,aAAY;;;;;;;;;;;;sCAGhB,8OAAC;;8CACC,8OAAC;oCAAM,WAAU;8CAA+C;;;;;;8CAGhE,8OAAC;oCACC,MAAK;oCACL,OAAO,SAAS,MAAM;oCACtB,UAAU,CAAC,IAAM,YAAY;4CAAE,GAAG,QAAQ;4CAAE,QAAQ,EAAE,MAAM,CAAC,KAAK;wCAAC;oCACnE,WAAU;oCACV,aAAY;;;;;;;;;;;;;;;;;;8BAKlB,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAK,WAAU;sCAA4B;;;;;;sCAC5C,8OAAC;4BACC,MAAK;4BACL,SAAS,IAAM,YAAY;oCAAE,GAAG,QAAQ;oCAAE,QAAQ,CAAC,SAAS,MAAM;gCAAC;4BACnE,WAAW,CAAC,0EAA0E,EACpF,SAAS,MAAM,GAAG,iBAAiB,eACnC;sCAEF,cAAA,8OAAC;gCACC,WAAW,CAAC,0EAA0E,EACpF,SAAS,MAAM,GAAG,kBAAkB,iBACpC;;;;;;;;;;;;;;;;;8BAKR,8OAAC;;sCACC,8OAAC;4BAAM,WAAU;sCAA+C;;;;;;sCAGhE,8OAAC;4BACC,OAAO,SAAS,KAAK;4BACrB,UAAU,CAAC,IAAM,YAAY;oCAAE,GAAG,QAAQ;oCAAE,OAAO,EAAE,MAAM,CAAC,KAAK;gCAAC;4BAClE,WAAU;4BACV,aAAY;4BACZ,MAAM;;;;;;;;;;;;8BAIV,8OAAC;oBACC,MAAK;oBACL,WAAU;8BACX;;;;;;;;;;;;;;;;;AAMT", "debugId": null}}, {"offset": {"line": 1546, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/RMR%20App%20Version%202/raising-my-rescue/src/app/clients/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { useApp } from '@/context/AppContext';\nimport Header from '@/components/layout/Header';\nimport AddModal from '@/components/AddModal';\nimport ClientModal from '@/components/modals/ClientModal';\nimport EditClientModal from '@/components/modals/EditClientModal';\nimport { Client } from '@/types';\n\nexport default function ClientsPage() {\n  const { state } = useApp();\n  const [searchQuery, setSearchQuery] = useState('');\n  const [showAddModal, setShowAddModal] = useState(false);\n  const [selectedClient, setSelectedClient] = useState<Client | null>(null);\n  const [showClientModal, setShowClientModal] = useState(false);\n  const [showEditClientModal, setShowEditClientModal] = useState(false);\n\n  const filteredClients = state.clients.filter(client => {\n    const searchTerm = searchQuery.toLowerCase();\n    return (\n      client.ownerName.toLowerCase().includes(searchTerm) ||\n      client.dogName.toLowerCase().includes(searchTerm)\n    );\n  });\n\n  const activeClients = filteredClients.filter(client => client.active);\n\n  const getAvatarText = (ownerName: string) => {\n    return ownerName.split(' ').map(name => name[0]).join('').toUpperCase();\n  };\n\n  const handleClientClick = (client: Client) => {\n    setSelectedClient(client);\n    setShowClientModal(true);\n  };\n\n  const handleAddClient = () => {\n    setShowAddModal(true);\n  };\n\n  const handleCloseAddModal = () => {\n    setShowAddModal(false);\n  };\n\n  const handleCloseClientModal = () => {\n    setShowClientModal(false);\n    setSelectedClient(null);\n  };\n\n  const handleEditClient = (client: Client) => {\n    setSelectedClient(client);\n    setShowEditClientModal(true);\n  };\n\n  const handleCloseEditClientModal = () => {\n    setShowEditClientModal(false);\n    setSelectedClient(null);\n  };\n\n  return (\n    <div className=\"min-h-screen bg-amber-800\" style={{ paddingTop: 'max(env(safe-area-inset-top), 20px)' }}>\n      <Header\n        title={`${activeClients.length} Active`}\n        showAddButton\n        addButtonText=\"Add Client\"\n        onAddClick={handleAddClient}\n        showSearch\n        onSearch={setSearchQuery}\n        searchPlaceholder=\"Search\"\n      />\n\n      <div className=\"px-4 py-4 bg-gray-50 min-h-screen\">\n        {/* Clients List */}\n        <div className=\"space-y-3\">\n          {activeClients.map((client) => (\n            <div\n              key={client.id}\n              onClick={() => handleClientClick(client)}\n              className=\"bg-white rounded-lg p-4 shadow-sm flex items-center justify-between active:bg-gray-50 transition-colors cursor-pointer\"\n            >\n              <div className=\"flex items-center space-x-3\">\n                <div className=\"w-10 h-10 bg-amber-800 rounded-full flex items-center justify-center text-white text-sm font-medium\">\n                  {client.avatar || getAvatarText(client.ownerName)}\n                </div>\n                <div>\n                  <h3 className=\"font-medium text-gray-900\">{client.ownerName}</h3>\n                  {client.dogName && (\n                    <p className=\"text-sm text-gray-500\">{client.dogName}</p>\n                  )}\n                </div>\n              </div>\n\n            </div>\n          ))}\n        </div>\n\n        {activeClients.length === 0 && (\n          <div className=\"text-center py-12\">\n            <p className=\"text-gray-500\">No clients found</p>\n          </div>\n        )}\n      </div>\n\n      <AddModal\n        isOpen={showAddModal}\n        onClose={handleCloseAddModal}\n        type=\"client\"\n      />\n\n      <ClientModal\n        client={selectedClient}\n        isOpen={showClientModal}\n        onClose={handleCloseClientModal}\n        onEditClient={handleEditClient}\n      />\n\n      <EditClientModal\n        client={selectedClient}\n        isOpen={showEditClientModal}\n        onClose={handleCloseEditClientModal}\n      />\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAPA;;;;;;;;AAUe,SAAS;IACtB,MAAM,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,6HAAA,CAAA,SAAM,AAAD;IACvB,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IACpE,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE/D,MAAM,kBAAkB,MAAM,OAAO,CAAC,MAAM,CAAC,CAAA;QAC3C,MAAM,aAAa,YAAY,WAAW;QAC1C,OACE,OAAO,SAAS,CAAC,WAAW,GAAG,QAAQ,CAAC,eACxC,OAAO,OAAO,CAAC,WAAW,GAAG,QAAQ,CAAC;IAE1C;IAEA,MAAM,gBAAgB,gBAAgB,MAAM,CAAC,CAAA,SAAU,OAAO,MAAM;IAEpE,MAAM,gBAAgB,CAAC;QACrB,OAAO,UAAU,KAAK,CAAC,KAAK,GAAG,CAAC,CAAA,OAAQ,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,IAAI,WAAW;IACvE;IAEA,MAAM,oBAAoB,CAAC;QACzB,kBAAkB;QAClB,mBAAmB;IACrB;IAEA,MAAM,kBAAkB;QACtB,gBAAgB;IAClB;IAEA,MAAM,sBAAsB;QAC1B,gBAAgB;IAClB;IAEA,MAAM,yBAAyB;QAC7B,mBAAmB;QACnB,kBAAkB;IACpB;IAEA,MAAM,mBAAmB,CAAC;QACxB,kBAAkB;QAClB,uBAAuB;IACzB;IAEA,MAAM,6BAA6B;QACjC,uBAAuB;QACvB,kBAAkB;IACpB;IAEA,qBACE,8OAAC;QAAI,WAAU;QAA4B,OAAO;YAAE,YAAY;QAAsC;;0BACpG,8OAAC,sIAAA,CAAA,UAAM;gBACL,OAAO,GAAG,cAAc,MAAM,CAAC,OAAO,CAAC;gBACvC,aAAa;gBACb,eAAc;gBACd,YAAY;gBACZ,UAAU;gBACV,UAAU;gBACV,mBAAkB;;;;;;0BAGpB,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;kCACZ,cAAc,GAAG,CAAC,CAAC,uBAClB,8OAAC;gCAEC,SAAS,IAAM,kBAAkB;gCACjC,WAAU;0CAEV,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACZ,OAAO,MAAM,IAAI,cAAc,OAAO,SAAS;;;;;;sDAElD,8OAAC;;8DACC,8OAAC;oDAAG,WAAU;8DAA6B,OAAO,SAAS;;;;;;gDAC1D,OAAO,OAAO,kBACb,8OAAC;oDAAE,WAAU;8DAAyB,OAAO,OAAO;;;;;;;;;;;;;;;;;;+BAXrD,OAAO,EAAE;;;;;;;;;;oBAoBnB,cAAc,MAAM,KAAK,mBACxB,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAE,WAAU;sCAAgB;;;;;;;;;;;;;;;;;0BAKnC,8OAAC,8HAAA,CAAA,UAAQ;gBACP,QAAQ;gBACR,SAAS;gBACT,MAAK;;;;;;0BAGP,8OAAC,2IAAA,CAAA,UAAW;gBACV,QAAQ;gBACR,QAAQ;gBACR,SAAS;gBACT,cAAc;;;;;;0BAGhB,8OAAC,+IAAA,CAAA,UAAe;gBACd,QAAQ;gBACR,QAAQ;gBACR,SAAS;;;;;;;;;;;;AAIjB", "debugId": null}}]}