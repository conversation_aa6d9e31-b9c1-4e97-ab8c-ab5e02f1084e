# `functions-js`

[![Coverage Status](https://coveralls.io/repos/github/supabase/functions-js/badge.svg?branch=main)](https://coveralls.io/github/supabase/functions-js?branch=main)

JS Client library to interact with Supabase Functions.

## Docs

<https://supabase.com/docs/reference/javascript/functions-invoke>

## testing

To run tests you will need Node 20+.

You are going to need docker daemon running to execute tests.

To start test run use the following command:

```sh
npm i
npm run test
```
