import { RealtimeClientOptions } from '@supabase/realtime-js';
import { SupabaseAuthClientOptions } from './types';
export declare const DEFAULT_HEADERS: {
    'X-Client-Info': string;
};
export declare const DEFAULT_GLOBAL_OPTIONS: {
    headers: {
        'X-Client-Info': string;
    };
};
export declare const DEFAULT_DB_OPTIONS: {
    schema: string;
};
export declare const DEFAULT_AUTH_OPTIONS: SupabaseAuthClientOptions;
export declare const DEFAULT_REALTIME_OPTIONS: RealtimeClientOptions;
//# sourceMappingURL=constants.d.ts.map