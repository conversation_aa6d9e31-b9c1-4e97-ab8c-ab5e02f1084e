-- RMR App Database Schema
-- Run this in your Supabase SQL Editor

-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Create session_type enum
CREATE TYPE session_type AS ENUM (
  'In-Person',
  'Online', 
  'Training',
  'Online Catchup',
  'Group',
  'Phone Call',
  'Coaching'
);

-- Create clients table
CREATE TABLE clients (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  first_name TEXT NOT NULL,
  last_name TEXT NOT NULL,
  email TEXT,
  phone TEXT,
  dog_name TEXT,
  other_dogs TEXT[],
  address TEXT,
  active BOOLEAN DEFAULT true,
  membership BOOLEAN DEFAULT false
);

-- Create sessions table
CREATE TABLE sessions (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  client_id UUID NOT NULL REFERENCES clients(id) ON DELETE CASCADE,
  session_type session_type NOT NULL,
  date DATE NOT NULL,
  time TIME NOT NULL,
  notes TEXT,
  quote DECIMAL(10,2),
  paid BOOLEAN DEFAULT false,
  behavioral_plan BOOLEAN DEFAULT false
);

-- Create indexes for better performance
CREATE INDEX idx_clients_active ON clients(active);
CREATE INDEX idx_clients_membership ON clients(membership);
CREATE INDEX idx_clients_name ON clients(first_name, last_name);
CREATE INDEX idx_sessions_client_id ON sessions(client_id);
CREATE INDEX idx_sessions_date ON sessions(date);
CREATE INDEX idx_sessions_type ON sessions(session_type);

-- Create updated_at trigger function
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ language 'plpgsql';

-- Create triggers to automatically update updated_at
CREATE TRIGGER update_clients_updated_at 
  BEFORE UPDATE ON clients 
  FOR EACH ROW 
  EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_sessions_updated_at 
  BEFORE UPDATE ON sessions 
  FOR EACH ROW 
  EXECUTE FUNCTION update_updated_at_column();

-- Enable Row Level Security (RLS)
ALTER TABLE clients ENABLE ROW LEVEL SECURITY;
ALTER TABLE sessions ENABLE ROW LEVEL SECURITY;

-- Create policies (for now, allow all operations - you can restrict later)
CREATE POLICY "Allow all operations on clients" ON clients
  FOR ALL USING (true);

CREATE POLICY "Allow all operations on sessions" ON sessions
  FOR ALL USING (true);

-- Insert some sample data (optional)
INSERT INTO clients (first_name, last_name, email, phone, dog_name, other_dogs, address, active, membership) VALUES
  ('John', 'Smith', '<EMAIL>', '555-0101', 'Buddy', NULL, '123 Main St, City, State 12345', true, true),
  ('Sarah', 'Johnson', '<EMAIL>', '555-0102', 'Luna', ARRAY['Max'], '456 Oak Ave, City, State 12345', true, false),
  ('Mike', 'Davis', '<EMAIL>', '555-0103', 'Charlie', ARRAY['Bella', 'Rocky'], '789 Pine Rd, City, State 12345', true, true);

-- Insert some sample sessions
INSERT INTO sessions (client_id, session_type, date, time, notes, quote, paid, behavioral_plan) VALUES
  ((SELECT id FROM clients WHERE first_name = 'John' AND last_name = 'Smith'), 'In-Person', '2024-06-10', '10:00', 'Initial consultation', 75.00, true, false),
  ((SELECT id FROM clients WHERE first_name = 'Sarah' AND last_name = 'Johnson'), 'Online', '2024-06-11', '14:00', 'Follow-up session', 50.00, false, true),
  ((SELECT id FROM clients WHERE first_name = 'Mike' AND last_name = 'Davis'), 'Training', '2024-06-12', '16:00', 'Group training session', 60.00, true, false);
